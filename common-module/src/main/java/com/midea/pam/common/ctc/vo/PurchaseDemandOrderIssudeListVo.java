package com.midea.pam.common.ctc.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
public class PurchaseDemandOrderIssudeListVo {
    private String id;
    private Integer approvedSupplierNumber;
    private Integer belongArea;
    private Integer closedAmount;
    private Integer closedQuantity;
    private Integer contractTotalAmount;
    private Long erpVendorId;
    private String createAt;
    private String createBy;
    private String currencyCode;
    private Boolean deletedFlag;
    private String deliveryTime;
    private Integer demandCost;
    private Boolean dispatchIs;      //是否急单
    private Long projectOuId; //业务实体id
    // private String endDateActive: undefined
    private String  erpCode;
    private Integer erpCodeIs;  //是否同步erp
    private String  erpVendorSiteId;  //供应商地点id
    private String materielDescr;
    private String materielId;
    private Integer needTotal;
    private Integer orderNum;
    private String pamCode;
    private String projectId;
    private String projectName;
    private String projectNum;
    private String projectOuName;
    private String projectWbsReceiptsId;
    private String publishTime;
    private Integer purchaseType;
    private String pvsStatus;
    private String receiptsId;
    private Integer releasedQuantity;
    private String requirementCode;
    private String requirementId;
    private Integer status;
    private String unit;
    private String unitCode;
    private Integer unreleasedAmount;
    private String updateBy;
    private String vendorCode;
    private String vendorId;
    private String vendorName;
    private String vendorSiteCode;
    private Integer wbsDemandOsCost;
    private Integer wbsRemainingDemandOsCost;
    private String wbsSummaryCode;

    private String model;
    private Long materialId;
    private String codingMiddleclass;
    private String materialType;
    private String brand;
    private String chartVersion;
    private String designReleaseLotNumber;

    private String discountMoney;
    //剩余需求预算占用金额（不含税）
    private String remainMoney;

    private BigDecimal budgetOccupiedAmount;
    private BigDecimal budgetOccupiedAmountTotal;

    private Long materialPurchaseRequirementId;

    private String activityCode;

    private Long orgId;

    private String figureNumber;

    private Long buyerId;

    private Integer wbsSummaryCodeStar;
    private Integer requirementCodeStar;
    private Integer designReleaseLotNumberStar;
    /**
     * 单价
     */
    private BigDecimal price;

    @ApiModelProperty("历史价")
    private BigDecimal historyPrice;

    @ApiModelProperty("历史价-本位币")
    private BigDecimal standardHistoryPrice;

    @ApiModelProperty("历史价订单行id")
    private Long historyPriceOrderDetailId;

    @ApiModelProperty("历史价项目编号")
    private String historyPriceProjectCode;

    @ApiModelProperty("历史价项目名称")
    private String historyPriceProjectName;

    @ApiModelProperty("错误信息")
    private String errorMsg;
}
