package com.midea.pam.common.ctc.dto;

import com.midea.pam.common.ctc.entity.PurchaseOrderDetail;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Getter
@Setter
@ApiModel(value = "PurchaseOrderDetailDto", description = "采购订单明细")
public class PurchaseOrderDetailDto extends PurchaseOrderDetail {

    //序号
    private Integer number;

    @ApiModelProperty("业务实体id")
    private Long ouId;

    @ApiModelProperty("业务实体名称")
    private String ouName;

    @ApiModelProperty("供应商编号")
    private String vendorCode;

    @ApiModelProperty("接收数量")
    private BigDecimal receiveCount;

    @ApiModelProperty("入库数量")
    private BigDecimal storageCount;

    @ApiModelProperty("采购订单号")
    private String num;

    @ApiModelProperty("采购员id")
    private Long buyerId;

    @ApiModelProperty("采购员")
    private String buyer;

    @ApiModelProperty("同步ERP=0：未同步， 1：已同步， 2：同步失败")
    private Integer syncStatus;

    @ApiModelProperty("退货数量")
    private BigDecimal returnCount;

    @ApiModelProperty("实际入库数量")
    private BigDecimal actualStorageCount;

    @ApiModelProperty("最早入库日期")
    private Date earliestStorageDate;

    @ApiModelProperty("订单状态")
    private Integer orderStatus;

    @ApiModelProperty("当前物料对应采购需求未下达量")
    private BigDecimal unreleasedAmount;

    @ApiModelProperty("币种")
    private String currency;

    @ApiModelProperty("汇率类型")
    private String conversionType;

    @ApiModelProperty("汇率")
    private String conversionRate;

    @ApiModelProperty("是否满足一揽子协议的有效期内，默认是true")
    private Boolean purchaseBpaPrice = true;

    @ApiModelProperty("承诺日期")
    private Date promisedDate;

    @ApiModelProperty(value = "批准供应商id")
    private String vendorId;

    @ApiModelProperty(value = "检验不合格数量")
    private BigDecimal rejectCount;
    @ApiModelProperty(value = "检测通过数量")
    private BigDecimal acceptCount;

    @ApiModelProperty(value = "采购订单合并行列表")
    private List<PurchaseOrderDetailDto> children;

    @ApiModelProperty(value = "剩余需求预算占用金额（不含税）")
    private BigDecimal remainMoney;

    @ApiModelProperty(value = " 是否急件(0:否;1:是)")
    private Boolean dispatchIs;
    @ApiModelProperty(value = "币种")
    private String currencyCode;
    @ApiModelProperty(value = "税率")
    private String taxRate;
    @ApiModelProperty(value = "订单类型")
    private String pricingType;
    @ApiModelProperty(value = "需求发布单据id")
    private Long requirementId;
    @ApiModelProperty(value = "erp订单状态=1-未发布 2-已发布 3-已回签")
    private Integer erpOrderStatus;

    @ApiModelProperty(value = "审批通过时间")
    private Date approvalTime;

    @ApiModelProperty(value = "实际下单数量")
    private BigDecimal actualOrderNum;

    @ApiModelProperty("创建人名称")
    private String createByName;

    @ApiModelProperty("项目业务实体名称")
    private String projectOuName;

    @ApiModelProperty("库存组织id")
    private Long organizationId;

    @ApiModelProperty("子库存代码")
    private String secondaryInventoryName;

    @ApiModelProperty(value = "是否子结构")
    private Boolean childIs;

    private Long originId;

    private List<Long> materialPurchaseRequirementIdList;

    @ApiModelProperty("订单行创建日期")
    private Date orderDetailCreateAt;

    @ApiModelProperty("订单行创建人名称")
    private String orderDetailCreateByName;

    @ApiModelProperty("错误信息")
    private String errorMsg;

    @ApiModelProperty("类型：0=已创建；1=待下达；2=待变更；3=创建中")
    private Integer type;

    @ApiModelProperty("供货比例")
    private BigDecimal proportion;

    @ApiModelProperty("采购单价，没有权限时返回***")
    private String costStr;

    @ApiModelProperty(value = "折扣，没有权限时返回***")
    private String discountStr;

    @ApiModelProperty(value = "折后价(不含税)，没有权限时返回***")
    private String discountPriceStr;

    @ApiModelProperty(value = "折后金额(不含税)，没有权限时返回***")
    private String discountMoneyStr;

    @ApiModelProperty(value = "单价(不含税)，没有权限时返回***")
    private String unitPriceStr;

    @ApiModelProperty("库存组织id")
    private Long orgId;

    @ApiModelProperty("项目业务实体id")
    private Long projectOuId;

    @ApiModelProperty("收货信息同步状态; 1:未同步、2:同步中、3:已同步、4:同步失败")
    private Integer syncDeliveryInfoStatus;

    @ApiModelProperty("需求预算占用金额(汇总)")
    private BigDecimal budgetOccupiedAmountTotal;

    @ApiModelProperty("历史价订单行id")
    private Long historyPriceOrderDetailId;

    @ApiModelProperty("历史价项目编号")
    private String historyPriceProjectCode;

    @ApiModelProperty("历史价项目名称")
    private String historyPriceProjectName;
}