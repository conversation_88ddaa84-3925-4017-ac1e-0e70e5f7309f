package com.midea.pam.basedata.service;

import com.github.pagehelper.PageInfo;
import com.midea.pam.common.basedata.dto.MaterialPriceConfigDetailExecuteDto;
import com.midea.pam.common.basedata.dto.MaterialPriceDetailDto;
import com.midea.pam.common.basedata.dto.MaterialPriceDto;
import com.midea.pam.common.basedata.dto.MaterialPricePlanDto;
import com.midea.pam.common.basedata.entity.MaterialPrice;
import com.midea.pam.common.basedata.entity.MaterialPriceConfigDetailExecute;
import com.midea.pam.common.crm.dto.PlanCurrencyCostDto;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface MaterialPriceService {

    /**
     * 物料价格列表查询
     *
     * @param dto
     * <AUTHOR>
     * @Date: 2023-05-17 上午 10:41:31
     */
    List<MaterialPriceDto> getList(MaterialPriceDto dto);

    /**
     * 物料价格-分页查询
     *
     * @param dto
     * <AUTHOR>
     * @Date: 2023-05-19 下午 1:48:31
     */
    PageInfo<MaterialPriceDto> page(MaterialPriceDto dto, Integer pageSize, Integer pageNum);

    List<MaterialPriceDto> exportList(MaterialPriceDto dto);

    MaterialPriceDetailDto getMaterialPriceDetailDtoById(Long id);

    void lock(List<Long> ids);

    void unLock(List<Long> ids);

    MaterialPriceConfigDetailExecuteDto configDetail(Long executeId);

    /**
     * 物料价格记录批量插入
     *
     * @param list
     * <AUTHOR>
     * @Date: 2023-05-19 下午 1:51:31
     */
    int batchInsert(List<MaterialPrice> list);


    void updateExecutionStatus(Integer executionStatus, List<Long> configHeaderIds);

    /**
     * 根据类型配置生成物料价格定时任务
     *
     * @param materialPriceConfigDetailExecutes
     * <AUTHOR>
     * @Date: 2023-05-19 下午 1:51:31
     */
    void generateMaterialPrice(List<MaterialPriceConfigDetailExecute> materialPriceConfigDetailExecutes);

    /**
     * 以organizationId + configHeaderId维度更新价格表信息
     *
     * @param materialPriceList
     */
    void insertOrUpdateMaterialPrice(List<MaterialPrice> materialPriceList);

    List<MaterialPricePlanDto> getMaterialPriceByFormConfig(Long unitId, Long ouId, String currency, String pamCode, String erpCode);

    /**
     * 根据库存组织+物料id+价格类型检查是否锁定
     *
     * @param materialId     物料id
     * @param configHeaderId 类型id
     * @param organizationId 库存组织id
     * @return 锁定则返回true
     */
    boolean checkPriceIsLock(Long materialId, Long configHeaderId, Long organizationId);

    PlanCurrencyCostDto importBatchFromExcel(PlanCurrencyCostDto dto);

    Map<String, BigDecimal> getMaterialPriceByConfigHeader(MaterialPriceDto dto);

    Map<String, MaterialPrice> getMaterialPriceEntityByConfigHeader(MaterialPriceDto dto);
}
