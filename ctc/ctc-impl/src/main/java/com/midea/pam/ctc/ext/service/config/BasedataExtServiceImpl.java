package com.midea.pam.ctc.ext.service.config;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.mcomponent.core.exception.MipException;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.basedata.dto.*;
import com.midea.pam.common.basedata.entity.BrandMaintenance;
import com.midea.pam.common.basedata.entity.CoaSubject;
import com.midea.pam.common.basedata.entity.CustTrxType;
import com.midea.pam.common.basedata.entity.Dict;
import com.midea.pam.common.basedata.entity.FeeType;
import com.midea.pam.common.basedata.entity.LaborExternalCost;
import com.midea.pam.common.basedata.entity.MaterialPrice;
import com.midea.pam.common.basedata.entity.OperatingUnit;
import com.midea.pam.common.basedata.entity.OrgUnit;
import com.midea.pam.common.basedata.entity.OrganizationRel;
import com.midea.pam.common.basedata.entity.ProjectProductMaintenance;
import com.midea.pam.common.basedata.entity.Storage;
import com.midea.pam.common.basedata.entity.Unit;
import com.midea.pam.common.basedata.entity.UserInfo;
import com.midea.pam.common.basedata.entity.UserMesDto;
import com.midea.pam.common.basedata.entity.VendorAslBasedata;
import com.midea.pam.common.basedata.entity.VendorMip;
import com.midea.pam.common.basedata.entity.VendorSiteBank;
import com.midea.pam.common.basedata.entity.VendorSiteBankForDisplay;
import com.midea.pam.common.basedata.query.*;
import com.midea.pam.common.constants.Constants;
import com.midea.pam.common.ctc.dto.BudgetHumanDto;
import com.midea.pam.common.ctc.dto.WorkingHourQueryDto;
import com.midea.pam.common.ctc.dto.WorkingHourResultDto;
import com.midea.pam.common.ctc.entity.BudgetItemSys;
import com.midea.pam.common.ctc.entity.BudgetItemSysExample;
import com.midea.pam.common.ctc.entity.OrganizationCustomDict;
import com.midea.pam.common.ctc.entity.PurchaseContract;
import com.midea.pam.common.ctc.entity.VendorAslCtc;
import com.midea.pam.common.ctc.vo.EsbUnitVo;
import com.midea.pam.common.enums.Code;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.enums.OrgCustomDictOrgFrom;
import com.midea.pam.common.exception.BizException;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.common.util.StringUtils;
import com.midea.pam.ctc.common.utils.DateUtil;
import com.midea.pam.ctc.feign.basedata.feign.BasedataMaterialFeignClient;
import com.midea.pam.ctc.mapper.BudgetItemSysMapper;
import com.midea.pam.ctc.mapper.PurchaseContractMapper;
import com.midea.pam.ctc.service.BasedataExtService;
import com.midea.pam.ctc.service.OrganizationCustomDictService;
import com.midea.pam.system.SystemContext;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.CollectionUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static com.midea.pam.common.util.StringUtils.buildGetUrl;

public class BasedataExtServiceImpl implements BasedataExtService {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    public static final Long NOT_EXIST_ID = -1L;

    @Resource
    private RestTemplate restTemplate;
    @Resource
    private OrganizationCustomDictService organizationCustomDictService;
    @Resource
    private BasedataMaterialFeignClient materialFeignClient;
    @Resource
    private PurchaseContractMapper purchaseContractMapper;
    @Resource
    private BudgetItemSysMapper budgetItemSysMapper;


    @Override
    public List<OrgUnit> getOrgUnit() {
        Map<String, Object> unitMap = new HashMap<>();
        unitMap.put("userId", SystemContext.getUserId());
        unitMap.put("unitId", SystemContext.getUnitId());
        final String url = com.midea.pam.common.util.StringUtils.buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/orgUnit/selectUserSecondUnit", unitMap);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        final DataResponse<List<OrgUnit>> response = JSON.parseObject(res, new TypeReference<DataResponse<List<OrgUnit>>>() {
        });
        return response.getData();
    }

    @Override
    public String getUnitSeqPerfix(Long unitId) {
        if (unitId == null || unitId <= 0) {
            return "";
        }
        // 缓存获取
        Unit unit = CacheDataUtils.findUnitById(unitId);
        // 获取父级
        if (unit != null && unit.getParentId() != null) {
            unitId = unit.getParentId();
            unit = CacheDataUtils.findUnitById(unitId);
        }
        if (unit != null && StringUtils.isNotEmpty(unit.getSeqPerfix())) {
            return unit.getSeqPerfix();
        }
        // 实时查表
        Map<String, Object> unitMap = new HashMap<>();
        unitMap.put("id", unitId);
        final String url = com.midea.pam.common.util.StringUtils.buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/unit/getUnitSeqPerfix", unitMap);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        final DataResponse<String> response = JSON.parseObject(res, new TypeReference<DataResponse<String>>() {
        });
        return response != null ? response.getData() : "";
    }

    @Override
    public List<EsbUnitVo> queryEsbUnit(String updateAt) {
        if (StringUtils.isEmpty(updateAt)) {
            return null;
        }
        Map<String, Object> unitMap = new HashMap<>();
        unitMap.put("updateAt", updateAt);
        final String url = com.midea.pam.common.util.StringUtils.buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/unit/queryEsbUnit", unitMap);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        final DataResponse<List<EsbUnitVo>> response = JSON.parseObject(res, new TypeReference<DataResponse<List<EsbUnitVo>>>() {
        });
        return response.getData();
    }

    @Override
    public VendorSiteBankDto getVendorSiteBankDto(Long id) {
        Map<String, Object> map = new HashMap<>();
        map.put("id", id);
        final String url = com.midea.pam.common.util.StringUtils.buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/vendor/view", map);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        final DataResponse<VendorSiteBankDto> response = JSON.parseObject(res, new TypeReference<DataResponse<VendorSiteBankDto>>() {
        });
        return response.getData();
    }

    @Override
    public VendorSiteBankDto getCurrencyCode(Long ouId, String vendorSiteCode, String vendorName) {
        Map<String, Object> map = new HashMap<>();
        map.put("ouId", ouId);
        map.put("vendorSiteCode", vendorSiteCode);
        map.put("vendorName", vendorName);
        final String url = StringUtils.buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/vendor/getCurrencyCode", map);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        final DataResponse<VendorSiteBankDto> response = JSON.parseObject(res, new TypeReference<DataResponse<VendorSiteBankDto>>() {
        });
        return response.getData();
    }


    @Override
    public OperatingUnit findOperatingUnitById(Long id) {
        Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        final String url = com.midea.pam.common.util.StringUtils.buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/operatingUnit/findOperatingUnitById", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        final DataResponse<OperatingUnit> response = JSON.parseObject(res, new TypeReference<DataResponse<OperatingUnit>>() {
        });
        return response.getData();
    }

    @Override
    public List<OperatingUnitDto> queryCurrentUnitOu() {
        Map<String, Object> param = new HashMap<>();
        param.put("unitId", SystemContext.getUnitId());
        final String url = com.midea.pam.common.util.StringUtils.buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/operatingUnit/queryCurrentUnitOu", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        final DataResponse<List<OperatingUnitDto>> response = JSON.parseObject(res, new TypeReference<DataResponse<List<OperatingUnitDto>>>() {
        });
        return response.getData();
    }

    @Override
    public List<OperatingUnitDto> queryCurrentUnitOu(Long unitId) {
        Map<String, Object> param = new HashMap<>();
        param.put("unitId", unitId);
        final String url = com.midea.pam.common.util.StringUtils.buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/operatingUnit/queryCurrentUnitOu", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        final DataResponse<List<OperatingUnitDto>> response = JSON.parseObject(res, new TypeReference<DataResponse<List<OperatingUnitDto>>>() {
        });
        return response.getData();
    }

    @Override
    public String queryCurrentStrUnitOu() {
        String ouIdStr = "";
        List<OperatingUnitDto> operatingUnits = queryCurrentUnitOu();
        if (null != operatingUnits) {
            for (OperatingUnit operatingUnit : operatingUnits) {
                ouIdStr = operatingUnit.getId() + "," + ouIdStr;
            }
        }
        return ouIdStr.length() > 0 ? ouIdStr.substring(0, ouIdStr.length() - 1) : "''";
    }

    @Override
    public Dict findDictById(Long id) {
        Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        final String url = com.midea.pam.common.util.StringUtils.buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/ltcDict/info", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        final DataResponse<Dict> response = JSON.parseObject(res, new TypeReference<DataResponse<Dict>>() {
        });
        return response.getData();
    }

    @Override
    public List<DictDto> findDictByType() {

        Map<String, Object> param = new HashMap<>();
        param.put("type", "third_bill_payment_type");
        final String url = com.midea.pam.common.util.StringUtils.buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/ltcDict/listByType", param);
        String res = restTemplate.getForObject(url, String.class);
        logger.info("获取第三方票据支付的数据为:{}", res);
        return JSON.parseObject(res, new TypeReference<List<DictDto>>() {
        });
    }

    @Override
    public List<WorkingHourResultDto> selectSubmitRemind(WorkingHourQueryDto query) {
        final Map<String, Object> param = new HashMap<>();
        param.put("startDate", DateUtil.format(query.getStartDate(), DateUtil.DATE_PATTERN));
        param.put("endDate", DateUtil.format(query.getEndDate(), DateUtil.DATE_PATTERN));
        final String url = com.midea.pam.common.util.StringUtils.buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/workingHourException/selectSubmitRemind", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        final DataResponse<List<WorkingHourResultDto>> response = JSON.parseObject(res, new TypeReference<DataResponse<List<WorkingHourResultDto>>>() {
        });
        return response.getData();
    }

    @Override
    public List<BankAccountDto> findBankAccount(BankAccountQuery query) {
        final Map<String, Object> param = new HashMap<>();
        param.put("bankAccountNum", query.getBankAccountNum());
        final String url = com.midea.pam.common.util.StringUtils.buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/bankAccount/findList", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        final DataResponse<List<BankAccountDto>> response = JSON.parseObject(res, new TypeReference<DataResponse<List<BankAccountDto>>>() {
        });
        return response.getData();
    }

    @Override
    public Dict findDictByTypeAndCode(String type, String code) {
        Map<String, Object> param = new HashMap<>();
        param.put("type", type);
        param.put("code", code);
        final String url = com.midea.pam.common.util.StringUtils.buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/ltcDict/listByTypeAndCode", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        final DataResponse<List<Dict>> response = JSON.parseObject(res, new TypeReference<DataResponse<List<Dict>>>() {
        });
        List<Dict> list = response.getData();
        if (list != null && list.size() != 0) {

            return list.get(0);
        }
        return null;
    }


    @Override
    public List<OrganizationCustomDict> getOrganizationCustomDictList(Long orgId, String orgFrom) {
//        Map<String, Object> param = new HashMap<>();
//        param.put("orgId", orgId);
//        param.put("orgFrom", StringUtils.hasText(orgFrom) ? orgFrom : OrgCustomDictOrgFrom.OU.code());
//        String url = com.midea.pam.common.util.StringUtils.buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/organizationCustomDict/queryByOrdId", param);
//        String res = restTemplate.getForEntity(url, String.class).getBody();
//        DataResponse<List<OrganizationCustomDict>> response = JSON.parseObject(res, new TypeReference<DataResponse<List<OrganizationCustomDict>>>() {
//        });
        final OrgCustomDictOrgFrom orgCustomDictOrgFrom = OrgCustomDictOrgFrom.getOrgCustomDictOrgFrom(orgFrom);
        if (orgCustomDictOrgFrom == null) {
            throw new MipException("类型参数有误,orgFrom为空");
        }
        List<OrganizationCustomDict> organizationCustomDictList = organizationCustomDictService.queryByOrdId(orgId, new String(), orgCustomDictOrgFrom);
        return organizationCustomDictList;
    }

    @Override
    public OrganizationCustomDict getOrganizationCustomDictByCode(Long orgId, String orgFrom, String code) {
        List<OrganizationCustomDict> list = getOrganizationCustomDictList(orgId, orgFrom);
        for (OrganizationCustomDict organizationCustomDict : list) {
            if (code.equals(organizationCustomDict.getCode())) {
                return organizationCustomDict;
            }
        }
        return null;
    }

    @Override
    public OrganizationCustomDict getOrganizationCustomDictByName(Long orgId, String orgFrom, String name) {
        List<OrganizationCustomDict> list = getOrganizationCustomDictList(orgId, orgFrom);
        for (OrganizationCustomDict organizationCustomDict : list) {
            if (name.equals(organizationCustomDict.getName())) {
                return organizationCustomDict;
            }
        }
        return null;
    }

    @Override
    public List<OrganizationRelDto> getOrganizationRel(OrganizationRelQuery query) {
        final Map<String, Object> param = new HashMap<>();
        param.put("pamEnabled", 0);
        param.put("operatingUnitId", query.getOperatingUnitId());
        param.put("organizationId", query.getOrganizationId());
        param.put("pageNum", 1);
        param.put("pageSize", 9999);
        String url = com.midea.pam.common.util.StringUtils.buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "organizationRel/list", param);
        String res = restTemplate.getForObject(url, String.class);
        PageInfo<OrganizationRelDto> data = JSON.parseObject(res, new TypeReference<PageInfo<OrganizationRelDto>>() {
        });
        return data.getList();
    }

    @Override
    public CoaSubject getCoaSubject(String flexValue) {
        Map<String, Object> param = new HashMap<>();
        param.put("flexValue", flexValue);
        final String url = com.midea.pam.common.util.StringUtils.buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/coaSubject/findByDescription", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        final DataResponse<CoaSubject> response = JSON.parseObject(res, new TypeReference<DataResponse<CoaSubject>>() {
        });
        return response.getData();
    }

    /**
     * 查询应收事务处理类型
     *
     * @param custTrxTypeIdName
     * @param trxType
     * @return
     */
    @Override
    public List<CustTrxType> getCustTrxType(String custTrxTypeIdName, String trxType, Long ouId) {
        CustTrxTypeQuery query = new CustTrxTypeQuery();
        query.setCustTrxTypeIdName(custTrxTypeIdName);
        query.setTrxType(trxType);
        query.setOperatingUnitId(ouId);
        query.setPageNum(1);
        query.setPageSize(1000);
        String url = String.format("%scustTrxType/selectPage", ModelsEnum.BASEDATA.getBaseUrl());
        String res = restTemplate.postForObject(url, query, String.class);
        PageInfo<CustTrxType> data = JSON.parseObject(res, new TypeReference<PageInfo<CustTrxType>>() {
        });
        return data.getList();
    }


    public List<ReceiptMethodDto> getReceiptMethod(Long operatingUnitId, Long receiptMethodId, String receiptMethodName, String currencyCode) {
        final Map<String, Object> param = new HashMap<>();
        param.put("orgId", operatingUnitId);
        param.put("receiptMethodId", receiptMethodId);
        param.put("receiptMethodName", receiptMethodName);
        param.put("currencyCode", currencyCode);
        String url = com.midea.pam.common.util.StringUtils.buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "receiptMethod/selectPage", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<PageInfo<ReceiptMethodDto>> response = JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<ReceiptMethodDto>>>() {
        });
        return response.getData().getList();
    }

    @Override
    public List<UnitDto> findUnitDtos(String id, String parentId) {
        Map<String, Object> unitMap = new HashMap<>();
        unitMap.put("id", id);
        unitMap.put("parentId", parentId);
        final String url = com.midea.pam.common.util.StringUtils.buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/unit/list", unitMap);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        final DataResponse<List<UnitDto>> response = JSON.parseObject(res, new TypeReference<DataResponse<List<UnitDto>>>() {
        });
        return response.getData();
    }

    @Override
    public List<Long> findUnitIds(Long parentId) {
        List<Long> result = new ArrayList<Long>();
        Map<String, Object> unitMap = new HashMap<>();
        unitMap.put("parentId", parentId);
        final String url = com.midea.pam.common.util.StringUtils.buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/unit/list", unitMap);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        final DataResponse<List<UnitDto>> response = JSON.parseObject(res, new TypeReference<DataResponse<List<UnitDto>>>() {
        });
        List<UnitDto> list = response.getData();
        if (list != null && list.size() > 0) {
            for (UnitDto unitDto : list) {
                result.add(unitDto.getId());
            }
        }
        if (result == null || result.size() == 0) {
            result.add(NOT_EXIST_ID);
        }
        return result;
    }

    @Override
    public String findStrUnitIds(Long parentId) {
        String unitIdStr = "";
        List<Long> unitIds = findUnitIds(parentId);
        if (null != unitIds) {
            for (Long unitId : unitIds) {
                unitIdStr = unitId + "," + unitIdStr;
            }
        }
        return unitIdStr.length() > 0 ? unitIdStr.substring(0, unitIdStr.length() - 1) : "''";
    }

    @Override
    public String findAuthorStrUnitIds() {
        String unitIdStr = "";
        List<Long> unitIds = SystemContext.getSecondUnits();
        if (null != unitIds) {
            for (Long unitId : unitIds) {
                unitIdStr = unitId + "," + unitIdStr;
            }
        }
        return unitIdStr.length() > 0 ? unitIdStr.substring(0, unitIdStr.length() - 1) : "''";
    }

    @Override
    public void syncVendorAsl(VendorAslCtc vendorAsl) {
        VendorAslBasedata vendorAslBasedata = new VendorAslBasedata();
        BeanUtils.copyProperties(vendorAsl, vendorAslBasedata);
        String url = String.format("%svendor/syncVendorAsl", ModelsEnum.BASEDATA.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, vendorAslBasedata, String.class);
    }

    @Override
    public LaborCostDto getLaborCost() {
        Map<String, Object> param = new HashMap<>();
        param.put("userId", SystemContext.getUserId());
        String url = com.midea.pam.common.util.StringUtils.buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/laborCost/detail", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<LaborCostDto> response = JSON.parseObject(res, new TypeReference<DataResponse<LaborCostDto>>() {
        });
        return response.getData();
    }

    @Override
    public List<LaborCostDto> getLaborCostList(Long unitId) {
        Map<String, Object> param = new HashMap<>();
        param.put("bizUnitId", unitId);
        String url = com.midea.pam.common.util.StringUtils.buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/laborCost/selectList", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<List<LaborCostDto>> response = JSON.parseObject(res, new TypeReference<DataResponse<List<LaborCostDto>>>() {
        });
        if (response != null && response.getCode() == 0) {
            return response.getData();
        }
        return Collections.emptyList();
    }

    @Override
    public LaborExternalCost getLaborExternalCost(Long costTypeId, String vendorName) {
        Map<String, Object> param = new HashMap<>();
        param.put("costTypeId", costTypeId);
        param.put("vendorName", vendorName);
        String url = StringUtils.buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "laborExternalCost/getLaborExternalCost", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<LaborExternalCost> response = JSON.parseObject(res, new TypeReference<DataResponse<LaborExternalCost>>() {
        });
        return response.getData();
    }


    @Override
    public UserDetailsDto findUserInfo(Long userId) {
        Map<String, Object> param = new HashMap<>();
        param.put("id", userId);
        final String url = com.midea.pam.common.util.StringUtils.buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/resource/user/findUserInfo", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        final DataResponse<UserDetailsDto> response = JSON.parseObject(res, new TypeReference<DataResponse<UserDetailsDto>>() {
        });
        return response.getData();

    }

    @Override
    public List<DictDto> getLtcDict(String type, String name, String code) {
        Map<String, Object> param = new HashMap<>();
        param.put("type", type);
        param.put("name", name);
        param.put("code", code);
        final String url = com.midea.pam.common.util.StringUtils.buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/ltcDict/getLtcDict", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        final DataResponse<List<DictDto>> response = JSON.parseObject(res, new TypeReference<DataResponse<List<DictDto>>>() {
        });
        return response.getData();
    }

    @Override
    public List<Storage> getNotNullByProCode(String projectCode, Long ouId) {
        Map<String, Object> param = new HashMap<>();
        param.put("projectCode", projectCode);
        param.put("ouId", ouId);
        final String url = com.midea.pam.common.util.StringUtils.buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/storage/getNotNullByProCode", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        final DataResponse<List<Storage>> response = JSON.parseObject(res, new TypeReference<DataResponse<List<Storage>>>() {
        });
        List<Storage> list = response.getData();
        if (!CollectionUtils.isEmpty(list)) {
            return list;
        }
        return null;
    }

    @Override
    public List<GlPeriodDto> getGlPeriod(Long ledgerId, String periodType, String periodName) {
        Map<String, Object> param = new HashMap<>();
        param.put("ledgerId", ledgerId);
        param.put("periodType", periodType);
        param.put("periodName", periodName);
        final String url = com.midea.pam.common.util.StringUtils.buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/glPeriod/selectList", param);
        String res = restTemplate.postForEntity(url, param, String.class).getBody();
        final List<GlPeriodDto> response = JSONArray.parseArray(res, GlPeriodDto.class);
        return response;
    }

    @Override
    public List<GlPeriodDto> getGlPeriodByOrganizationId(Long organizationId, String periodType, String periodName) {
        Map<String, Object> param = new HashMap<>();
        param.put("organizationId", organizationId);
        param.put("periodType", periodType);
        param.put("periodName", periodName);
        final String url = com.midea.pam.common.util.StringUtils.buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/glPeriod/selectList", param);
        String res = restTemplate.postForEntity(url, param, String.class).getBody();
        final List<GlPeriodDto> response = JSONArray.parseArray(res, GlPeriodDto.class);
        return response;
    }

    @Override
    public List<GlPeriodDto> getGlPeriod2(Long ledgerId, String periodType, String closingStatus) {
        Map<String, Object> param = new HashMap<>();
        param.put("ledgerId", ledgerId);
        param.put("periodType", periodType);
        param.put("closingStatus", closingStatus);
        final String url = com.midea.pam.common.util.StringUtils.buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/glPeriod/selectList", param);
        String res = restTemplate.postForEntity(url, param, String.class).getBody();
        final List<GlPeriodDto> response = JSONArray.parseArray(res, GlPeriodDto.class);
        return response;
    }

    @Override
    public List<VendorSiteBank> getByErpVendorSiteIds(String erpVendorSiteIds) {
        Map<String, Object> param = new HashMap<>();
        param.put("erpVendorSiteIds", erpVendorSiteIds);
        final String url = com.midea.pam.common.util.StringUtils.buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/vendorSiteBank/getByErpVendorSiteIds", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        final DataResponse<List<VendorSiteBank>> response = JSON.parseObject(res, new TypeReference<DataResponse<List<VendorSiteBank>>>() {
        });
        List<VendorSiteBank> list = response.getData();
        if (!CollectionUtils.isEmpty(list)) {
            return list;
        }
        return null;
    }

    @Override
    public List<BudgetHumanDto> queryLabCostByUserId(String priceType, String userIds, String unitId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("priceType", priceType);
        param.put("userIds", userIds);
        param.put("unitId", unitId);
        param.put("check", false);
        final String url = StringUtils.buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "laborCost/queryLabCostByUserId", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        final DataResponse<List<BudgetHumanDto>> dataResponse =
                JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<List<BudgetHumanDto>>>() {
                });
        List<BudgetHumanDto> list = dataResponse.getData();
        if (!CollectionUtils.isEmpty(list)) {
            return list;
        }
        return null;
    }

    @Override
    public List<UserInfo> getAvailableUser() {
        final Map<String, Object> param = new HashMap<>();
        final String url = StringUtils.buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "employeeInfo/queryForIhr", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        final DataResponse<List<UserInfo>> dataResponse =
                JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<List<UserInfo>>>() {
                });
        List<UserInfo> list = dataResponse.getData();
        if (!CollectionUtils.isEmpty(list)) {
            return list;
        }
        return new ArrayList<>();
    }

    @Override
    public PageInfo<GlDailyRateDto> queryGlDailyRate(String fromCurrency, String toCurrency, Date exchangeDate) {
        Map<String, Object> param = new HashMap<>();
        param.put("fromCurrency", fromCurrency);
        param.put("toCurrency", toCurrency);
        param.put("exchangeDate", exchangeDate);
        final String url = com.midea.pam.common.util.StringUtils.buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/glDailyRate/selectPage", param);
        String res = restTemplate.postForEntity(url, param, String.class).getBody();
        PageInfo<GlDailyRateDto> data = JSON.parseObject(res, new TypeReference<PageInfo<GlDailyRateDto>>() {
        });
        return data;
    }

    @Override
    public List<GlPeriodDto> getGlPeriod1(Long ledgerId, String periodType, String periodName, Long periodYear, String glPeriodDate) {
        Map<String, Object> param = new HashMap<>();
        param.put("ledgerId", ledgerId);
        param.put("periodType", periodType);
        param.put("periodName", periodName);
        param.put("periodYear", periodYear);
        param.put("startDate", DateUtil.parseDate(glPeriodDate));
        param.put("endDate", DateUtil.parseDate(glPeriodDate));
        final String url = com.midea.pam.common.util.StringUtils.buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/glPeriod/selectList", param);
        String res = restTemplate.postForEntity(url, param, String.class).getBody();
        final List<GlPeriodDto> response = JSONArray.parseArray(res, GlPeriodDto.class);
        return response;
    }

    @Override
    public List<UserInfoDto> listInfo1(
            String orgId,
            String orgName,
            String name,
            String username,
            Long userId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("orgId", orgId);
        param.put("name", name);
        param.put("username", username);
        param.put("userId", userId);
        param.put("orgName", orgName);
        String url = com.midea.pam.common.util.StringUtils.buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/resource/user/getListUserInfo1", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        final DataResponse<List<UserInfoDto>> dataResponse =
                JSON.parseObject(res, new TypeReference<DataResponse<List<UserInfoDto>>>() {
                });
        List<UserInfoDto> list = dataResponse.getData();
        if (!CollectionUtils.isEmpty(list)) {
            return list;
        }

        return new ArrayList<>();
    }


    @Override
    public List<OrgLaborCostTypeSetDTO> findOrgLaborCostTypes(Long companyId,
                                                              Long unitId,
                                                              String hrOrgName,
                                                              String laborCostTypeCode,
                                                              String laborCostTypeCodes,
                                                              String unitIds) {

        final Map<String, Object> param = new HashMap<>();
        param.put("companyId", companyId);
        param.put("unitId", unitId);
        param.put("hrOrgName", hrOrgName);
        param.put("laborCostTypeCode", laborCostTypeCode);
        param.put("laborCostTypeCodes", laborCostTypeCodes);
        param.put("unitIds", unitIds);
        final String url = com.midea.pam.common.util.StringUtils.buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "orgLaborCostTypeSet/findOrgLaborCostTypes", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();

        final DataResponse<List<OrgLaborCostTypeSetDTO>> dataResponse =
                JSON.parseObject(res, new TypeReference<DataResponse<List<OrgLaborCostTypeSetDTO>>>() {
                });
        List<OrgLaborCostTypeSetDTO> list = dataResponse.getData();
        if (!CollectionUtils.isEmpty(list)) {
            return list;
        }

        return new ArrayList<>();
    }

    @Override
    public List<LaborCostRankRealMonthDetailDto> getValidDetails(Long bizUnitId, String month, String levelName) {
        final Map<String, Object> param = new HashMap<>();
        param.put("bizUnitId", bizUnitId);
        param.put("month", month);
        param.put("levelName", levelName);
        final String url = com.midea.pam.common.util.StringUtils.buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "laborCostRealMonth/getValidDetails", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();

        final DataResponse<List<LaborCostRankRealMonthDetailDto>> dataResponse =
                JSON.parseObject(res, new TypeReference<DataResponse<List<LaborCostRankRealMonthDetailDto>>>() {
                });
        List<LaborCostRankRealMonthDetailDto> list = dataResponse.getData();
        if (!CollectionUtils.isEmpty(list)) {
            return list;
        }

        return new ArrayList<>();
    }


    @Override
    public List<VendorSiteBankDto> getVendorSiteBankDtoList(Long id, String vendorCode, Long operatingUnitId) {
        Map<String, Object> map = new HashMap<>();
        map.put("id", id);
        map.put("vendorCode", vendorCode);
        map.put("operatingUnitId", operatingUnitId);
        final String url = com.midea.pam.common.util.StringUtils.buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/vendor/viewList", map);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        final DataResponse<List<VendorSiteBankDto>> dataResponse =
                JSON.parseObject(res, new TypeReference<DataResponse<List<VendorSiteBankDto>>>() {
                });
        List<VendorSiteBankDto> list = dataResponse.getData();
        if (!CollectionUtils.isEmpty(list)) {
            return list;
        }
        return new ArrayList<>();
    }

    @Override
    public Map<Long, UserMesDto> getUserLaborCostType(String userIds) {
        Map<String, Object> map = new HashMap<>(1);
        map.put("userIds", userIds);
        String url = StringUtils.buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/laborCost/getUserLaborCostType", map);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<Map<Long, UserMesDto>> dataResponse =
                JSON.parseObject(res, new TypeReference<DataResponse<Map<Long, UserMesDto>>>() {
                });
        Map<Long, UserMesDto> result = dataResponse.getData();
        if (!CollectionUtils.isEmpty(result)) {
            return result;
        }
        return new HashMap<>(1);
    }

    @Override
    public List<OrganizationRelDto> selectCurrentOrganization() {
        List<Long> ouIds = SystemContext.getOus();
        ouIds.add(-1L); //防止空值
        StringBuffer ouIdStr = new StringBuffer();
        ouIds.stream().forEach(ouId -> {
            ouIdStr.append(ouId).append(",");
        });
        final Map<String, Object> params = new HashMap<>();
        params.put("pamEnabled", 0);
        params.put("ous", ouIdStr);
        String url = StringUtils.buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "organizationRel/selectOrganization", params);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        List<OrganizationRelDto> data = JSON.parseObject(res, new TypeReference<List<OrganizationRelDto>>() {
        });
        if (!CollectionUtils.isEmpty(data)) {
            return data;
        }
        return new ArrayList<>();
    }

    @Override
    public OrgLaborCostTypeSetDTO findOrgLaborCostTypeSetById(Long id, Long ouId) {
        final Map<String, Object> params = new HashMap<>();
        params.put("id", id);
        params.put("ouId", ouId);
        String url = com.midea.pam.common.util.StringUtils.buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/orgLaborCostTypeSet/findOrgLaborCostTypeSetById", params);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        final DataResponse<OrgLaborCostTypeSetDTO> dataResponse = JSON.parseObject(res, new TypeReference<DataResponse<OrgLaborCostTypeSetDTO>>() {
        });
        OrgLaborCostTypeSetDTO orgLaborCostTypeSet = dataResponse.getData();
        return orgLaborCostTypeSet;
    }

    public VendorSiteBankDto getVendorSiteBankInfo(Long ouId, String vendorBankNum, String vendorCode, String accountName) {
        if (ouId == null || StringUtils.isEmpty(vendorBankNum)
                || StringUtils.isEmpty(vendorCode)
                || StringUtils.isEmpty(accountName)) {
            return null;
        }
        Map<String, Object> map = new HashMap<>();
        map.put("ouId", ouId);
        map.put("vendorBankNum", vendorBankNum);
        map.put("vendorCode", vendorCode);
        map.put("accountName", accountName);
        final String url = com.midea.pam.common.util.StringUtils.buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/vendor/getVendorSiteBankInfo", map);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        final DataResponse<VendorSiteBankDto> response = JSON.parseObject(res, new TypeReference<DataResponse<VendorSiteBankDto>>() {
        });
        return response.getData();
    }

    public VendorSiteBankForDisplay getVendorSiteBankStatus(Long ouId, String vendorCode, String erpVendorSiteId) {
        Map<String, Object> map = new HashMap<>();
        map.put("ouId", ouId);
        map.put("vendorCode", vendorCode);
        map.put("erpVendorSiteId", erpVendorSiteId);
        final String url = com.midea.pam.common.util.StringUtils.buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/vendor/selectPageNew", map);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        PageInfo<VendorSiteBankForDisplay> data = JSON.parseObject(res, new TypeReference<PageInfo<VendorSiteBankForDisplay>>() {
        });
        if (data != null && ListUtils.isNotEmpty(data.getList())) {
            return data.getList().get(0);
        } else {
            return null;
        }
    }

    @Override
    public List<VendorSiteBankDto> getVendorSiteBankByCodes(List<String> vendorCodes) {
        if (ListUtils.isEmpty(vendorCodes)) {
            return Collections.emptyList();
        }
        String url = ModelsEnum.BASEDATA.getBaseUrl() + "vendor/getVendorSiteBankByCodes";
        String res = restTemplate.postForObject(url, vendorCodes, String.class);
        return JSON.parseObject(res, new TypeReference<List<VendorSiteBankDto>>() {
        });
    }

    @Override
    public List<VendorSiteBankDto> getVendorSiteBankByCode(List<String> vendorCodes) {
        if (ListUtils.isEmpty(vendorCodes)) {
            return Collections.emptyList();
        }
        String url = ModelsEnum.BASEDATA.getBaseUrl() + "vendor/getVendorSiteBankByCodes1";
        String res = restTemplate.postForObject(url, vendorCodes, String.class);
        DataResponse<List<VendorSiteBankDto>> response = JSON.parseObject(res, new TypeReference<DataResponse<List<VendorSiteBankDto>>>() {
        });
        if (response != null && response.getCode() == 0) {
            return response.getData();
        } else {
            return Collections.emptyList();
        }
    }

    @Override
    public List<OperatingUnitDto> getOuInfoByOuIds(List<Long> ouIds) {
        if (ListUtils.isEmpty(ouIds)) {
            return Collections.emptyList();
        }
        String url = ModelsEnum.BASEDATA.getBaseUrl() + "operatingUnit/getOperatingUnitByIds";
        String res = restTemplate.postForObject(url, ouIds, String.class);
        DataResponse<List<OperatingUnitDto>> response = JSON.parseObject(res, new TypeReference<DataResponse<List<OperatingUnitDto>>>() {
        });
        if (response != null && response.getCode() == 0) {
            return response.getData();
        } else {
            return Collections.emptyList();
        }
    }

    @Override
    public Unit selectByPrimaryKey(Long id) {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        final String url = com.midea.pam.common.util.StringUtils.buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "unit/queryUnitById", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<Unit> unitDataResponse = JSON.parseObject(res, new TypeReference<DataResponse<Unit>>() {
        });
        return unitDataResponse.getData();
    }

    @Override
    public List<StorageInventoryDto> selectStorageInventoryList(String inventoryType) {
        final Map<String, Object> param = new HashMap<>();
        param.put("inventoryType", inventoryType);
        final String url = com.midea.pam.common.util.StringUtils.buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/storageInventory/selectInventory", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<List<StorageInventoryDto>> listDataResponse = JSON.parseObject(res, new TypeReference<DataResponse<List<StorageInventoryDto>>>() {
        });
        return listDataResponse.getData();
    }

    /**
     * 根据子库类型和子库资产查询可用子库列表
     *
     * @param inventoryType
     * @param assetInventory
     * @return
     */
    @Override
    public List<StorageInventoryDto> selectStorageInventoryListByTypeAndAssetInventory(String inventoryType, String assetInventory) {
        final Map<String, Object> param = new HashMap<>();
        param.put("inventoryType", inventoryType);
        param.put("assetInventory", assetInventory);
        final String url = com.midea.pam.common.util.StringUtils.buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/storageInventory/selectInventoryByTypeAndAssetInventory", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<List<StorageInventoryDto>> listDataResponse = JSON.parseObject(res, new TypeReference<DataResponse<List<StorageInventoryDto>>>() {
        });
        return listDataResponse.getData();
    }

    public LaborCostDto getLaborCostYL(Long bizUnitId, Integer type, Long userId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("type", type);
        param.put("bizUnitId", bizUnitId);
        param.put("userId", userId);
        String url = com.midea.pam.common.util.StringUtils.buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/laborCost/detailYL", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<LaborCostDto> response = JSON.parseObject(res, new TypeReference<DataResponse<LaborCostDto>>() {
        });
        return response.getData();
    }

    @Override
    public List<Long> getMaterialAvailableOuIds() {
        List<OperatingUnitDto> operatingUnitDtoList = getMaterialAvailableOperatingUnitList();
        if (ListUtils.isNotEmpty(operatingUnitDtoList)) {
            return operatingUnitDtoList.stream().map(OperatingUnitDto::getOperatingUnitId).distinct().collect(Collectors.toList());
        } else {
            return new ArrayList<>();
        }
    }

    /**
     * 查询配置了组织参数[物料编码规则]的使用单位所属的生效业务实体
     *
     * @return
     */
    @Override
    public List<OperatingUnitDto> getMaterialAvailableOperatingUnitList() {
        final OrgCustomDictOrgFrom orgCustomDictOrgFrom = OrgCustomDictOrgFrom.getOrgCustomDictOrgFrom("company");
        if (orgCustomDictOrgFrom == null) {
            return new ArrayList<>();
        }
        Map<Long, Set<String>> unitMap = organizationCustomDictService.selectByName("物料编码规则", null, orgCustomDictOrgFrom);
        if (unitMap == null || unitMap.size() <= 0) {
            return new ArrayList<>();
        }
        Set<Long> unitIds = unitMap.keySet();
        List<OperatingUnitDto> operatingUnitDtoList = new ArrayList<>();
        for (Long unitId : unitIds) {
            operatingUnitDtoList.addAll(queryCurrentUnitOu(unitId));
        }

        return operatingUnitDtoList;
    }

    @Override
    public ProjectProductMaintenance getProjectProductMaintenance(Long id) {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        String url = com.midea.pam.common.util.StringUtils.buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/projectProductMaintenance/detail", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<ProjectProductMaintenance> response = JSON.parseObject(res, new TypeReference<DataResponse<ProjectProductMaintenance>>() {
        });
        return response.getData();
    }

    @Override
    public List<BrandMaintenance> getBrandMaintenance() {
        final Map<String, Object> param = new HashMap<>();
        String url = com.midea.pam.common.util.StringUtils.buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/brandMaintenance/selectList", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<List<BrandMaintenance>> response = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<List<BrandMaintenance>>>() {
        });
        return response.getData();
    }

    @Override
    public void updateMilepostDesignPlanMiddle(Long markId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("markId", markId);
        String url = com.midea.pam.common.util.StringUtils.buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/milepostDesignPlanImport/updateMiddle", param);
        restTemplate.getForEntity(url, String.class);
    }

    @Override
    public List<CustTrxType> getByOuId(Long ouId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("ouId", ouId);
        String url = com.midea.pam.common.util.StringUtils.buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/custTrxType/getByOuId", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<List<CustTrxType>> response = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<List<CustTrxType>>>() {
        });
        return response.getData();
    }

    @Override
    public List<OrganizationRel> queryByOuId(Long ouId) {
        final Map<String, Object> params = new HashMap<>();
        params.put("ouId", ouId);
        String url = com.midea.pam.common.util.StringUtils.buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "organizationRel/queryByOuId", params);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<List<OrganizationRel>> listDataResponse = JSON.parseObject(res, new TypeReference<DataResponse<List<OrganizationRel>>>() {
        });
        return listDataResponse.getData();
    }

    @Override
    public String queryLocalCurrencyByOuId(Long ouId) {
        String localCurrency = "";
        List<OrganizationRel> organizationRels = queryByOuId(ouId);
        if (!CollectionUtils.isEmpty(organizationRels) && organizationRels.get(0) != null) {
            localCurrency = organizationRels.get(0).getCurrency();
        }
        if (StringUtils.isEmpty(localCurrency)) {
            throw new BizException(Code.ERROR, String.format("根据业务实体查询本位币失败，请联系IT人员进行维护！"));
        }
        return localCurrency;
    }

    @Override
    public Long getIdByOrganizationIdAndItemCode(Long organizationId, String itemCode) {
        final Map<String, Object> params = new HashMap<>();
        params.put("organizationId", organizationId);
        params.put("itemCode", itemCode);
        String url = com.midea.pam.common.util.StringUtils.buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "material/getIdByOrganizationIdAndItemCode", params);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<Long> dataResponse = JSON.parseObject(res, new TypeReference<DataResponse<Long>>() {
        });
        return dataResponse.getData();
    }

    @Override
    public List<MaterialDto> listByItemCodesAndOrganizationIds(List<MaterialDto> materialDtos) {
        if (ListUtils.isEmpty(materialDtos)) {
            return Collections.emptyList();
        }
        String url = ModelsEnum.BASEDATA.getBaseUrl() + "material/listByItemCodesAndOrganizationIds";
        String res = restTemplate.postForObject(url, materialDtos, String.class);
        DataResponse<List<MaterialDto>> response = JSON.parseObject(res, new TypeReference<DataResponse<List<MaterialDto>>>() {
        });
        if (response != null && response.getCode() == 0) {
            return response.getData();
        } else {
            return Collections.emptyList();
        }
    }

    @Override
    public Map<String, OrganizationRel> queryCurrentUnitOrganization() {
        Map<String, Object> param = new HashMap<>();
        param.put("unitId", SystemContext.getUnitId());
        String url = StringUtils.buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/operatingUnit/queryCurrentUnitOu", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<List<OperatingUnitDto>> response = JSON.parseObject(res, new TypeReference<DataResponse<List<OperatingUnitDto>>>() {
        });
        Map<String, OrganizationRel> organizationMap = new HashMap<>();
        List<OperatingUnitDto> operatingUnitDtoList = response.getData();
        if (CollectionUtils.isEmpty(operatingUnitDtoList)) {
            return organizationMap;
        }
        for (OperatingUnitDto operatingUnitDto : operatingUnitDtoList) {
            List<OrganizationRel> rels = operatingUnitDto.getRels();
            for (OrganizationRel organizationRel : rels) {
                if (organizationRel.getOrganizationId() != null) {
                    organizationMap.put(organizationRel.getOrganizationCode(), organizationRel);
                }
            }
        }
        return organizationMap;
    }

    @Override
    public List<OrganizationRel> getValidOrganization() {
        Map<String, Object> params = new HashMap<>();
        String url = com.midea.pam.common.util.StringUtils.buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "organizationRel/getValidOrganization", params);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<List<OrganizationRel>> dataResponse = JSON.parseObject(res, new TypeReference<DataResponse<List<OrganizationRel>>>() {
        });
        return dataResponse.getData();
    }

    @Override
    public List<OperatingUnitDto> getOperatingUnitByUnitId(Long unitId) {
        unitId = Optional.ofNullable(unitId).orElse(SystemContext.getUnitId());
        String url = ModelsEnum.BASEDATA.getBaseUrl() + "operatingUnit/getOperatingUnitByUnitId?unitId=" + unitId;
        String res = restTemplate.getForObject(url, String.class);
        DataResponse<List<OperatingUnitDto>> response = JSON.parseObject(res, new TypeReference<DataResponse<List<OperatingUnitDto>>>() {
        });
        if (response == null || response.getCode() != 0) {
            logger.error("ou查询失败：{}", response != null ? response.getMsg() : res);
            throw new BizException(Code.ERROR, "ou查询失败");
        }
        return response.getData();
    }

    @Override
    public List<OperatingUnitDto> getAllOperatingUnit() {
        String url = ModelsEnum.BASEDATA.getBaseUrl() + "operatingUnit/getAllOperatingUnit";
        String res = restTemplate.getForObject(url, String.class);
        DataResponse<List<OperatingUnitDto>> response = JSON.parseObject(res, new TypeReference<DataResponse<List<OperatingUnitDto>>>() {
        });
        if (response == null || response.getCode() != 0) {
            logger.error("ou查询失败：{}", response != null ? response.getMsg() : res);
            throw new BizException(Code.ERROR, "ou查询失败");
        }
        return response.getData();
    }

    @Override
    public OperatingUnitDto queryUnitByOuId(Long ouId) {
        String url = ModelsEnum.BASEDATA.getBaseUrl() + "operatingUnit/queryUnitByOuId?ouId=" + ouId;
        String res = restTemplate.getForObject(url, String.class);
        DataResponse<List<OperatingUnitDto>> response = JSON.parseObject(res, new TypeReference<DataResponse<List<OperatingUnitDto>>>() {
        });
        if (response == null || response.getCode() != 0) {
            logger.error("ou单位查询失败：{}", response != null ? response.getMsg() : res);
            throw new BizException(Code.ERROR, "ou单位查询失败");
        }
        if (!response.getData().isEmpty()) {
            return response.getData().get(0);
        }
        return null;
    }

    @Override
    public GlDailyRateDto queryGlDailyRate(GlDailyRateQuery query) {
        String url = ModelsEnum.BASEDATA.getBaseUrl() + "glDailyRate/selectDailyRate";
        String res = restTemplate.postForObject(url, query, String.class);
        if (StringUtils.isEmpty(res)) {
            logger.error("汇率查询失败");
            throw new BizException(Code.ERROR, "汇率查询失败");
        }
        List<GlDailyRateDto> glDailyRateDtoList = JSON.parseObject(res, new TypeReference<List<GlDailyRateDto>>() {
        });
        if (!glDailyRateDtoList.isEmpty()) {
            return glDailyRateDtoList.get(0);
        }
        return null;
    }

    @Override
    public DiffCompanyLaborCostSetDTO getDiffCompanyLaborCostSet(Long bizUnitId, Long fullPayOuId, Long ouId) {
        final Map<String, Object> params = new HashMap<>();
        params.put("projectCompanyId", bizUnitId);
        params.put("fullPayOuId", fullPayOuId);
        params.put("ouId", ouId);
        String url = com.midea.pam.common.util.StringUtils.buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/orgLaborCostTypeSet/getDiffCompanyLaborCostSet", params);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        final DataResponse<DiffCompanyLaborCostSetDTO> dataResponse = JSON.parseObject(res, new TypeReference<DataResponse<DiffCompanyLaborCostSetDTO>>() {
        });
        DiffCompanyLaborCostSetDTO diffCompanyLaborCostSet = dataResponse.getData();
        return diffCompanyLaborCostSet;
    }

    @Override
    public BankAccountDto getBankAccountByBankAccountId(Long bankAccountId) {
        Map<String, Object> param = new HashMap<>();
        param.put("bankAccountId", bankAccountId);
        String url = StringUtils.buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "bankAccount/getBankAccountByBankAccountId", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<BankAccountDto> response = JSON.parseObject(res, new TypeReference<DataResponse<BankAccountDto>>() {
        });
        return response.getData();
    }

    @Override
    public List<VendorMip> getVendorMipList(String vendorCodeStr) {
        Map<String, Object> param = new HashMap<>();
        param.put("vendorCodeStr", vendorCodeStr);
        String url = StringUtils.buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "vendor/listVendorMip", param);
        final String vendorMipRes = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<List<VendorMip>> response = JSON.parseObject(vendorMipRes, new TypeReference<DataResponse<List<VendorMip>>>() {
        });
        return response.getData();
    }

    @Override
    public Map<String, BigDecimal> getMaterialPriceByConfigHeader(MaterialPriceDto dto) {
        String url = ModelsEnum.BASEDATA.getBaseUrl() + "materialPrice/getMaterialPriceByConfigHeader";
        String res = restTemplate.postForObject(url, dto, String.class);
        DataResponse<Map<String, BigDecimal>> response = JSON.parseObject(res, new TypeReference<DataResponse<Map<String, BigDecimal>>>() {
        });
        if (response != null && response.getCode() == 0) {
            return response.getData();
        } else {
            return Collections.emptyMap();
        }
    }

    @Override
    public Map<String, MaterialPrice> getMaterialPriceEntityByConfigHeader(MaterialPriceDto dto) {
        String url = ModelsEnum.BASEDATA.getBaseUrl() + "materialPrice/getMaterialPriceByConfigHeader";
        String res = restTemplate.postForObject(url, dto, String.class);
        DataResponse<Map<String, MaterialPrice>> response = JSON.parseObject(res, new TypeReference<DataResponse<Map<String, MaterialPrice>>>() {
        });
        if (response != null && response.getCode() == 0) {
            return response.getData();
        } else {
            return Collections.emptyMap();
        }
    }

    @Override
    public Map<Long, Long> getMaterialByIds(List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return Collections.emptyMap();
        }
        String url = ModelsEnum.BASEDATA.getBaseUrl() + "material/getOrgByIds";
        String res = restTemplate.postForObject(url, idList, String.class);
        DataResponse<Map<Long, Long>> response = JSON.parseObject(res, new TypeReference<DataResponse<Map<Long, Long>>>() {
        });
        if (response != null && response.getCode() == 0) {
            return response.getData();
        } else {
            return Collections.emptyMap();
        }
    }

    @Override
    public List<FeeItemDto> queryFeeItemList(Long unitId, String name) {
        final Map<String, Object> param = new HashMap<>();
        param.put(Constants.Page.PAGE_NUM, 1);
        param.put(Constants.Page.PAGE_SIZE, 10000);
        param.put(Constants.Page.NAME, name);
        param.put("authority", Boolean.FALSE);
        param.put("unitId", unitId);
        final String url = StringUtils.buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/feeItem/pageByProject", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<PageInfo<FeeItemDto>> response = JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<FeeItemDto>>>() {
        });
        if (response == null || response.getCode() != 0) {
            logger.error("查询费用类型失败：{}", response != null ? response.getMsg() : res);
            return Collections.emptyList();
        }
        return response.getData().getList();
    }

    @Override
    public List<CoaSubjectDto> selectAllUnitCoaSubject() {
        final Map<String, Object> param = new HashMap<>();
        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "unit/selectAllUnitCoaSubject", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<List<CoaSubjectDto>> response = JSON.parseObject(res, new TypeReference<DataResponse<List<CoaSubjectDto>>>() {
        });
        if (response == null || response.getCode() != 0) {
            throw new BizException(Code.ERROR, String.format("查询项目业务分类组织关系的产品段信息失败"));
        }
        return response.getData();
    }

    /**
     * 根据供应商id查询默认预算项目号
     *
     * @param vendorId
     * @return key: dict.Name,value: dict.Value
     */
    public Pair<String, String> getBudgetProjectNumberByVendorId(Long purchaseContractId, Long vendorId, Long ouId) {
        if (Objects.nonNull(purchaseContractId)) {
            PurchaseContract purchaseContract = purchaseContractMapper.selectByPrimaryKey(purchaseContractId);
            if (Objects.nonNull(purchaseContract)) {
                vendorId = purchaseContract.getVendorId();
            }
        }

        HashMap<String, Object> params = new HashMap<>();
        params.put("vendorId", vendorId);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/vendor/getVendorById", params);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        if (org.springframework.util.StringUtils.isEmpty(res)) {
            logger.error("根据供应商id查询默认预算项目号-获取供应商信息失败,vendorId:{}", vendorId);
            return null;
        }
        VendorSiteBank vendorSiteBank = JSON.parseObject(res, new TypeReference<DataResponse<VendorSiteBank>>() {
        }).getData();
        String vendorTypeName = vendorSiteBank.getVendorTypeName();
        String dictTypeName = "";
        if (com.midea.pam.common.util.StringUtils.isNotEmpty(vendorTypeName) && vendorTypeName.contains("内部供应商")) {
            dictTypeName = "默认预算项目号-内部供应商";
        } else {
            dictTypeName = "默认预算项目号-外部供应商";
        }
        OrganizationCustomDict organizationCustomDict = getOrganizationCustomDictByName(ouId, OrgCustomDictOrgFrom.OU.code(), dictTypeName);
        if (organizationCustomDict != null) {
            //从预算项目号表中取对应名称
            String budgetItemName = "";
            BudgetItemSysExample budgetItemSysExample = new BudgetItemSysExample();
            budgetItemSysExample.createCriteria().andDeletedFlagEqualTo(0)
                    .andBudgetItemCodeEqualTo(organizationCustomDict.getValue());
            List<BudgetItemSys> budgetItemSysList = budgetItemSysMapper.selectByExample(budgetItemSysExample);
            if (!CollectionUtils.isEmpty(budgetItemSysList)) {
                budgetItemName = Optional.ofNullable(budgetItemSysList.get(0)).map(BudgetItemSys::getBudgetItemName).orElse("");
            }
            return Pair.of(budgetItemName, organizationCustomDict.getValue());
        }
        return null;
    }

    @Override
    public Map<String, String> getMaterialShelvesByOrgIdAndItemCode(Long organizationId, List<String> itemCodes) {
        if (Objects.isNull(organizationId)) {
            logger.error("getMaterialShelvesByOrgIdAndItemCode organizationId is null");
            return Collections.emptyMap();
        }
        if (ListUtils.isEmpty(itemCodes)) {
            logger.error("getMaterialShelvesByOrgIdAndItemCode itemCodes is null");
            return Collections.emptyMap();
        }
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        MultiValueMap<String, Object> requestParams = new LinkedMultiValueMap<>();
        requestParams.add("organizationId", String.valueOf(organizationId));

        for (String itemCode : itemCodes) {
            requestParams.add("itemCodeList", itemCode);
        }

        HttpEntity<MultiValueMap<String, Object>> requestEntity =
                new HttpEntity<>(requestParams, headers);

        String url = ModelsEnum.BASEDATA.getBaseUrl() + "/material/getMaterialShelvesByOrgIdAndItemCode";
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, requestEntity, String.class);
        if (StringUtils.isNotEmpty(responseEntity.getBody())) {
            DataResponse<Map<String, String>> response = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Map<String, String>>>() {
            });
            if (response != null && response.getCode() == 0) {
                return response.getData();
            }
        }
        return Collections.emptyMap();
    }

    @Override
    public List<MaterialDto> getMaterialListByMaterialIds(List<Long> materialIds) {
        if (CollectionUtils.isEmpty(materialIds)) {
            return Collections.emptyList();
        }
        final String url = String.format("%smaterial/getByIds", ModelsEnum.BASEDATA.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, materialIds, String.class);
        if (StringUtils.isNotEmpty(responseEntity.getBody())) {
            DataResponse<List<MaterialDto>> response = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<List<MaterialDto>>>() {
            });
            if (response != null && response.getCode() == 0) {
                return response.getData();
            }
        }
        return Collections.emptyList();
    }

    @Override
    public List<FeeType> getFeeTypeByIds(List<Long> feeTypeIds) {
        if (CollectionUtils.isEmpty(feeTypeIds)) {
            return Collections.emptyList();
        }
        final String url = String.format("%sfeeType/getByIds", ModelsEnum.BASEDATA.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, feeTypeIds, String.class);
        if (StringUtils.isNotEmpty(responseEntity.getBody())) {
            DataResponse<List<FeeType>> response = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<List<FeeType>>>() {
            });
            if (response != null && response.getCode() == 0) {
                return response.getData();
            }
        }
        return Collections.emptyList();
    }

    @Override
    public List<OrganizationRelDto> getOrganizationRelByCompanyCode(List<String> companyCodeList) {
        final Map<String, Object> param = new HashMap<>();
        param.put("companyCodeList", org.apache.commons.lang3.StringUtils.join(companyCodeList, ","));
        param.put("pageNum", 1);
        param.put("pageSize", 9999);
        String url = com.midea.pam.common.util.StringUtils.buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "organizationRel/list", param);
        String res = restTemplate.getForObject(url, String.class);
        PageInfo<OrganizationRelDto> data = JSON.parseObject(res, new TypeReference<PageInfo<OrganizationRelDto>>() {
        });
        return data.getList();
    }

    @Override
    public List<InventoryDto> selectInventoryList(InventoryQuery query) {
        if(Objects.nonNull(query)){
            query.setPageSize(9999);
            query.setPageNum(1);

            // 将InventoryQuery对象转换为Map参数
            Map<String, Object> param = new HashMap<>();
            param.put("organizationId", query.getOrganizationId());
            param.put("organizationCode", query.getOrganizationCode());
            param.put("organizationName", query.getOrganizationName());
            param.put("secondaryInventoryName", query.getSecondaryInventoryName());
            param.put("locator", query.getLocator());
            param.put("locatorDisableDateIsNull", query.getLocatorDisableDateIsNull());
            param.put("description", query.getDescription());
            param.put("locDescription", query.getLocDescription());
            param.put("isAccurate", query.getIsAccurate());
            param.put("pageSize", query.getPageSize());
            param.put("pageNum", query.getPageNum());

            // 使用GET请求调用inventory/list接口
            String url = com.midea.pam.common.util.StringUtils.buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "inventory/list", param);
            ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
            if (StringUtils.isNotEmpty(responseEntity.getBody())) {
                // 直接解析PageInfo<InventoryDto>，因为接口返回的不是DataResponse包装格式
                PageInfo<InventoryDto> pageInfo = JSON.parseObject(responseEntity.getBody(), new TypeReference<PageInfo<InventoryDto>>() {
                });
                if (pageInfo != null && pageInfo.getList() != null) {
                    return pageInfo.getList();
                }
            }
        }
        return Collections.emptyList();
    }
}
