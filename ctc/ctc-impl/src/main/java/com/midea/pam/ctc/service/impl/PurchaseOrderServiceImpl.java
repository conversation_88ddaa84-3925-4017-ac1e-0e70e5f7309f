package com.midea.pam.ctc.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.midea.mcomponent.core.exception.MipException;
import com.midea.mcomponent.core.util.StringUtil;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.basedata.dto.DictDto;
import com.midea.pam.common.basedata.dto.GlDailyRateDto;
import com.midea.pam.common.basedata.dto.MaterialDto;
import com.midea.pam.common.basedata.dto.MaterialQueryDTO;
import com.midea.pam.common.basedata.dto.MaterialPriceDto;
import com.midea.pam.common.basedata.dto.OperatingUnitDto;
import com.midea.pam.common.basedata.dto.OrganizationRelDto;
import com.midea.pam.common.basedata.dto.UserInfoDto;
import com.midea.pam.common.basedata.dto.VendorSiteBankDto;
import com.midea.pam.common.basedata.entity.Material;
import com.midea.pam.common.basedata.entity.MaterialPrice;
import com.midea.pam.common.basedata.entity.OperatingUnit;
import com.midea.pam.common.basedata.entity.OrganizationRel;
import com.midea.pam.common.basedata.entity.Unit;
import com.midea.pam.common.basedata.entity.UserInfo;
import com.midea.pam.common.basedata.query.GlDailyRateQuery;
import com.midea.pam.common.constants.AduitAtta;
import com.midea.pam.common.constants.Constants;
import com.midea.pam.common.constants.ResponseMap;
import com.midea.pam.common.ctc.dto.CtcAttachmentDto;
import com.midea.pam.common.ctc.dto.PurchaseContractBudgetDto;
import com.midea.pam.common.ctc.dto.PurchaseDemandOrderIssudeDto;
import com.midea.pam.common.ctc.dto.PurchaseMaterialReleaseDetailDto;
import com.midea.pam.common.ctc.dto.PurchaseMaterialRequirementDto;
import com.midea.pam.common.ctc.dto.PurchaseOrderChangeHistoryDto;
import com.midea.pam.common.ctc.dto.PurchaseOrderChangeRecordDto;
import com.midea.pam.common.ctc.dto.PurchaseOrderDetailChangeHistoryDto;
import com.midea.pam.common.ctc.dto.PurchaseOrderDetailDto;
import com.midea.pam.common.ctc.dto.PurchaseOrderDto;
import com.midea.pam.common.ctc.dto.PurchaseOrderReceiptsDto;
import com.midea.pam.common.ctc.dto.PurchaseOrderStandardTermsDto;
import com.midea.pam.common.ctc.dto.PurchaseOrderTitleDto;
import com.midea.pam.common.ctc.dto.SaveBatchPurchaseOrderRecordDto;
import com.midea.pam.common.ctc.dto.SdpBuyersDto;
import com.midea.pam.common.ctc.dto.SdpCheckDto;
import com.midea.pam.common.ctc.dto.SdpTradeResultResponseEleDto;
import com.midea.pam.common.ctc.dto.StandardTermsContentDto;
import com.midea.pam.common.ctc.dto.StandardTermsDto;
import com.midea.pam.common.ctc.entity.AsyncRequestResult;
import com.midea.pam.common.ctc.entity.CtcAttachment;
import com.midea.pam.common.ctc.entity.CtcAttachmentExample;
import com.midea.pam.common.ctc.entity.OrganizationCustomDict;
import com.midea.pam.common.ctc.entity.Project;
import com.midea.pam.common.ctc.entity.ProjectExample;
import com.midea.pam.common.ctc.entity.ProjectProfit;
import com.midea.pam.common.ctc.entity.ProjectProfitExample;
import com.midea.pam.common.ctc.entity.ProjectWbsBudget;
import com.midea.pam.common.ctc.entity.ProjectWbsReceipts;
import com.midea.pam.common.ctc.entity.ProjectWbsReceiptsBudget;
import com.midea.pam.common.ctc.entity.ProjectWbsReceiptsBudgetExample;
import com.midea.pam.common.ctc.entity.PurchaseBpaPrice;
import com.midea.pam.common.ctc.entity.PurchaseBpaPriceExample;
import com.midea.pam.common.ctc.entity.PurchaseContractStamp;
import com.midea.pam.common.ctc.entity.PurchaseContractStampExample;
import com.midea.pam.common.ctc.entity.PurchaseMaterialReleaseDetail;
import com.midea.pam.common.ctc.entity.PurchaseMaterialReleaseDetailExample;
import com.midea.pam.common.ctc.entity.PurchaseMaterialRequirement;
import com.midea.pam.common.ctc.entity.PurchaseOrder;
import com.midea.pam.common.ctc.entity.PurchaseOrderChangeHistory;
import com.midea.pam.common.ctc.entity.PurchaseOrderChangeHistoryExample;
import com.midea.pam.common.ctc.entity.PurchaseOrderChangeRecord;
import com.midea.pam.common.ctc.entity.PurchaseOrderChangeRecordExample;
import com.midea.pam.common.ctc.entity.PurchaseOrderDetail;
import com.midea.pam.common.ctc.entity.PurchaseOrderDetailChangeHistory;
import com.midea.pam.common.ctc.entity.PurchaseOrderDetailChangeHistoryExample;
import com.midea.pam.common.ctc.entity.PurchaseOrderDetailExample;
import com.midea.pam.common.ctc.entity.PurchaseOrderExample;
import com.midea.pam.common.ctc.entity.PurchaseOrderMerge;
import com.midea.pam.common.ctc.entity.PurchaseOrderMergeExample;
import com.midea.pam.common.ctc.entity.PurchaseOrderRecord;
import com.midea.pam.common.ctc.entity.PurchaseOrderRecordExample;
import com.midea.pam.common.ctc.entity.PurchaseOrderStandardTerms;
import com.midea.pam.common.ctc.entity.PurchaseOrderStandardTermsContent;
import com.midea.pam.common.ctc.entity.PurchaseOrderStandardTermsContentExample;
import com.midea.pam.common.ctc.entity.PurchaseOrderStandardTermsExample;
import com.midea.pam.common.ctc.entity.ResendExecute;
import com.midea.pam.common.ctc.entity.ResendExecuteExample;
import com.midea.pam.common.ctc.entity.SdpBuyers;
import com.midea.pam.common.ctc.entity.SdpBuyersExample;
import com.midea.pam.common.ctc.entity.StandardTermsDeviation;
import com.midea.pam.common.ctc.entity.StandardTermsDeviationExample;
import com.midea.pam.common.ctc.entity.SupplierQualityDeactivation;
import com.midea.pam.common.ctc.excelVo.PurchaseOrderDetailImportVO;
import com.midea.pam.common.ctc.excelVo.PurchaseOrderImportVO;
import com.midea.pam.common.ctc.query.PurchaseBpaPriceQuery;
import com.midea.pam.common.ctc.vo.AssociateOccupiedBudgetDetailsVo;
import com.midea.pam.common.ctc.vo.PurchaseDemandOrderIssudeListVo;
import com.midea.pam.common.ctc.vo.PurchaseDemandOrderIssudeTitleVo;
import com.midea.pam.common.ctc.vo.PurchaseDemandOrderIssudeVo;
import com.midea.pam.common.ctc.vo.PurchaseOrderDetailPdfVO;
import com.midea.pam.common.ctc.vo.PurchaseOrderPdfVO;
import com.midea.pam.common.ctc.vo.SdpCheckVo;
import com.midea.pam.common.enums.BusinessTypeEnums;
import com.midea.pam.common.enums.Code;
import com.midea.pam.common.enums.CodePrefix;
import com.midea.pam.common.enums.CommonStatus;
import com.midea.pam.common.enums.DictType;
import com.midea.pam.common.enums.ErpOrderStatus;
import com.midea.pam.common.enums.ErrorCode;
import com.midea.pam.common.enums.FreezeFlag;
import com.midea.pam.common.enums.HistoryType;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.enums.OrderStatusEnum;
import com.midea.pam.common.enums.OrgCustomDictOrgFrom;
import com.midea.pam.common.enums.PricingTypeEnum;
import com.midea.pam.common.enums.SyncDeliveryInfoStatusEnum;
import com.midea.pam.common.esb.constant.EsbConstant;
import com.midea.pam.common.esb.vo.ERPMassQueryReturnVo;
import com.midea.pam.common.exception.BizException;
import com.midea.pam.common.util.Asserts;
import com.midea.pam.common.util.BigDecimalUtils;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.DistributedCASLock;
import com.midea.pam.common.util.GenerateIDUtils;
import com.midea.pam.common.util.JsonUtils;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.common.util.StringUtils;
import com.midea.pam.common.util.Utils;
import com.midea.pam.ctc.common.enums.ContractTermsFlgEnum;
import com.midea.pam.ctc.common.enums.CorUserStatus;
import com.midea.pam.ctc.common.enums.CtcAttachmentModule;
import com.midea.pam.ctc.common.enums.DeletedFlag;
import com.midea.pam.ctc.common.enums.ProjectWbsReceiptsBudgetDemandTypeEnums;
import com.midea.pam.ctc.common.enums.PurchaseContractStampType;
import com.midea.pam.ctc.common.enums.PurchaseMaterialRequirementPurchaseTypeEnums;
import com.midea.pam.ctc.common.enums.PurchaseOrderChangeRecordStatus;
import com.midea.pam.ctc.common.enums.PurchaseOrderDetailChangeHistoryChangeStatus;
import com.midea.pam.ctc.common.enums.PurchaseOrderDetailStatus;
import com.midea.pam.ctc.common.enums.PurchaseOrderStatus;
import com.midea.pam.ctc.common.enums.PurchaseOrderSyncStatus;
import com.midea.pam.ctc.common.enums.StandardTermsEnum;
import com.midea.pam.ctc.common.redis.RedisContant;
import com.midea.pam.ctc.common.redis.RedisUtilServer;
import com.midea.pam.ctc.common.utils.BeanConverter;
import com.midea.pam.ctc.common.utils.DateUtil;
import com.midea.pam.ctc.common.utils.EnumUtil;
import com.midea.pam.ctc.common.utils.ListUtil;
import com.midea.pam.ctc.contract.service.PurchaseOrderCallBackService;
import com.midea.pam.ctc.ext.service.config.HandleDispatcher;
import com.midea.pam.ctc.mapper.AgencySynInfoMapper;
import com.midea.pam.ctc.mapper.CtcAttachmentMapper;
import com.midea.pam.ctc.mapper.ProjectMapper;
import com.midea.pam.ctc.mapper.ProjectProfitMapper;
import com.midea.pam.ctc.mapper.ProjectWbsReceiptsBudgetMapper;
import com.midea.pam.ctc.mapper.ProjectWbsReceiptsMapper;
import com.midea.pam.ctc.mapper.PurchaseBpaPriceMapper;
import com.midea.pam.ctc.mapper.PurchaseContractBudgetExtMapper;
import com.midea.pam.ctc.mapper.PurchaseContractStampMapper;
import com.midea.pam.ctc.mapper.PurchaseMaterialReleaseDetailMapper;
import com.midea.pam.ctc.mapper.PurchaseMaterialRequirementExtMapper;
import com.midea.pam.ctc.mapper.PurchaseMaterialRequirementMapper;
import com.midea.pam.ctc.mapper.PurchaseOrderChangeHistoryExtMapper;
import com.midea.pam.ctc.mapper.PurchaseOrderChangeHistoryMapper;
import com.midea.pam.ctc.mapper.PurchaseOrderChangeRecordMapper;
import com.midea.pam.ctc.mapper.PurchaseOrderDetailChangeHistoryExtMapper;
import com.midea.pam.ctc.mapper.PurchaseOrderDetailChangeHistoryMapper;
import com.midea.pam.ctc.mapper.PurchaseOrderDetailExtMapper;
import com.midea.pam.ctc.mapper.PurchaseOrderDetailMapper;
import com.midea.pam.ctc.mapper.PurchaseOrderExtMapper;
import com.midea.pam.ctc.mapper.PurchaseOrderMapper;
import com.midea.pam.ctc.mapper.PurchaseOrderMergeExtMapper;
import com.midea.pam.ctc.mapper.PurchaseOrderMergeMapper;
import com.midea.pam.ctc.mapper.PurchaseOrderRecordMapper;
import com.midea.pam.ctc.mapper.PurchaseOrderStandardTermsContentMapper;
import com.midea.pam.ctc.mapper.PurchaseOrderStandardTermsMapper;
import com.midea.pam.ctc.mapper.SdpBuyersMapper;
import com.midea.pam.ctc.mapper.StandardTermsDeviationMapper;
import com.midea.pam.ctc.mapper.SupplierQualityDeactivationExtMapper;
import com.midea.pam.ctc.mapper.VendorAslCtcMapper;
import com.midea.pam.ctc.service.AdapterService;
import com.midea.pam.ctc.service.AgencySynService;
import com.midea.pam.ctc.service.AsyncRequestResultService;
import com.midea.pam.ctc.service.BasedataExtService;
import com.midea.pam.ctc.service.CtcAttachmentService;
import com.midea.pam.ctc.service.EsbService;
import com.midea.pam.ctc.service.IFlowExceptionReBuildService;
import com.midea.pam.ctc.service.MaterialExtService;
import com.midea.pam.ctc.service.OrganizationCustomDictService;
import com.midea.pam.ctc.service.ProjectBusinessService;
import com.midea.pam.ctc.service.ProjectProfitService;
import com.midea.pam.ctc.service.ProjectService;
import com.midea.pam.ctc.service.PurchaseBpaPriceService;
import com.midea.pam.ctc.service.PurchaseMaterialRequirementService;
import com.midea.pam.ctc.service.PurchaseOrderDetailService;
import com.midea.pam.ctc.service.PurchaseOrderService;
import com.midea.pam.ctc.service.ResendExecuteService;
import com.midea.pam.ctc.service.SdpBuyersService;
import com.midea.pam.ctc.service.SdpCarrierServicel;
import com.midea.pam.ctc.service.StorageInventoryExtService;
import com.midea.pam.ctc.service.VendorAslService;
import com.midea.pam.ctc.service.event.PurchaseOrderChangeSyncEvent;
import com.midea.pam.ctc.service.event.PurchaseOrderSyncEvent;
import com.midea.pam.ctc.service.event.PushToErpSyncEvent;
import com.midea.pam.ctc.wbs.service.ProjectWbsBudgetService;
import com.midea.pam.framework.core.exception.ApplicationBizException;
import com.midea.pam.framework.core.exception.Guard;
import com.midea.pam.system.SystemContext;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.validation.ConstraintViolation;
import javax.validation.Validator;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.midea.pam.ctc.common.redis.RedisContant.PURCHASE_DEMAND_ORDER_ISSUED;

public class PurchaseOrderServiceImpl implements PurchaseOrderService {

    private static final Logger logger = LoggerFactory.getLogger(PurchaseOrderServiceImpl.class);

    private static final Long MAX_WAIT_TIME = (long) 1000 * 60 * 10;//10分钟

    public static final Long NOT_EXIST_ID = -1L;

    @Resource
    private PurchaseOrderMapper purchaseOrderMapper;
    @Resource
    private PurchaseOrderExtMapper purchaseOrderExtMapper;
    @Resource
    private ProjectWbsBudgetService projectWbsBudgetService;
    @Resource
    private PurchaseOrderDetailService purchaseOrderDetailService;
    @Resource
    private PurchaseOrderDetailMapper purchaseOrderDetailMapper;
    @Resource
    private ProjectMapper projectMapper;
    @Resource
    private PurchaseMaterialRequirementService purchaseMaterialRequirementService;
    @Resource
    private ProjectService projectService;
    @Resource
    private BasedataExtService basedataExtService;
    @Resource
    private VendorAslService vendorAslService;
    @Resource
    private ProjectProfitService projectProfitService;
    @Resource
    private PurchaseMaterialRequirementExtMapper purchaseMaterialRequirementExtMapper;
    @Resource
    private RestTemplate restTemplate;
    @Resource
    private AsyncRequestResultService asyncRequestResultService;
    @Resource
    private EsbService esbService;
    @Resource
    private AgencySynInfoMapper agencySynInfoMapper;
    @Resource
    private AgencySynService agencySynService;
    @Resource
    private AdapterService adapterService;
    @Resource
    ResendExecuteService resendExecuteService;
    @Resource
    private ApplicationEventPublisher applicationEventPublisher;
    @Resource
    private OrganizationCustomDictService organizationCustomDictService;
    @Resource
    private PurchaseOrderRecordMapper purchaseOrderRecordMapper;
    @Resource
    private MaterialExtService materialExtService;
    @Resource
    private PurchaseBpaPriceMapper purchaseBpaPriceMapper;
    @Resource
    private RedisUtilServer redisUtilServer;
    @Resource
    private CtcAttachmentMapper ctcAttachmentMapper;
    @Resource
    private PurchaseOrderDetailExtMapper purchaseOrderDetailExtMapper;
    @Resource
    private PurchaseOrderMergeExtMapper purchaseOrderMergeExtMapper;
    @Resource
    private PurchaseOrderMergeMapper purchaseOrderMergeMapper;
    @Resource
    private SupplierQualityDeactivationExtMapper supplierQualityDeactivationExtMapper;
    @Resource
    private PurchaseOrderChangeRecordMapper purchaseOrderChangeRecordMapper;
    @Resource
    private PurchaseOrderDetailChangeHistoryMapper purchaseOrderDetailChangeHistoryMapper;
    @Resource
    private PurchaseOrderChangeHistoryMapper purchaseOrderChangeHistoryMapper;
    @Resource
    private PurchaseOrderChangeHistoryExtMapper purchaseOrderChangeHistoryExtMapper;
    @Resource
    private PurchaseOrderDetailChangeHistoryExtMapper purchaseOrderDetailChangeHistoryExtMapper;
    @Resource
    private PurchaseBpaPriceService purchaseBpaPriceService;
    @Resource
    private Validator validator;
    @Resource
    private PurchaseContractStampMapper purchaseContractStampMapper;
    @Resource
    CtcAttachmentService ctcAttachmentService;
    @Resource
    private VendorAslCtcMapper vendorAslCtcMapper;
    @Resource
    private PurchaseOrderCallBackService purchaseOrderCallBackService;
    @Resource
    private SdpBuyersService buyersService;
    @Resource
    private SdpBuyersMapper buyersMapper;
    @Resource
    private PurchaseMaterialReleaseDetailMapper purchaseMaterialReleaseDetailMapper;
    @Resource
    private StorageInventoryExtService storageInventoryExtService;
    @Resource
    private ProjectProfitMapper projectProfitMapper;
    @Resource
    private ProjectWbsReceiptsBudgetMapper projectWbsReceiptsBudgetMapper;
    @Resource
    private ProjectBusinessService projectBusinessService;
    @Resource
    private PurchaseMaterialRequirementMapper purchaseMaterialRequirementMapper;
    @Resource
    private PurchaseContractBudgetExtMapper purchaseContractBudgetExtMapper;
    @Resource
    private ProjectWbsReceiptsMapper projectWbsReceiptsMapper;
    @Resource
    private SdpCarrierServicel sdpCarrierServicel;
    @Resource
    private PurchaseOrderStandardTermsMapper purchaseOrderStandardTermsMapper;
    @Resource
    private PurchaseOrderStandardTermsContentMapper purchaseOrderStandardTermsContentMapper;
    @Resource
    private StandardTermsDeviationMapper standardTermsDeviationMapper;


    @Override
    public PurchaseOrderDto add(PurchaseOrderDto dto) {
        dto.setDeletedFlag(DeletedFlag.VALID.code());
        dto.setSyncSourceSystem("PAM");
        PurchaseOrder entity = BeanConverter.copy(dto, PurchaseOrder.class);
        purchaseOrderMapper.insert(entity);
        BeanConverter.copy(entity, dto);
        return dto;
    }

    @Override
    public PurchaseOrderDto update(PurchaseOrderDto dto) {
        PurchaseOrder entity = BeanConverter.copy(dto, PurchaseOrder.class);
        purchaseOrderMapper.updateByPrimaryKeySelective(entity);
        BeanConverter.copy(entity, dto);
        return dto;
    }

    @Override
    public PurchaseOrderDto save(PurchaseOrderDto dto, Long userBy) {
        if (dto.getId() == null) {
            dto.setCreateBy(userBy);
            return this.add(dto);
        } else {
            dto.setUpdateBy(userBy);
            return this.update(dto);
        }
    }

    @Override
    public PurchaseOrderDto getById(Long id) {
        Asserts.notEmpty(id, ErrorCode.ID_NOT_NULL);
        PurchaseOrder entity = purchaseOrderMapper.selectByPrimaryKey(id);
        PurchaseOrderDto dto = BeanConverter.copy(entity, PurchaseOrderDto.class);
        packageDto(dto);
        return dto;
    }

    @Override
    public List<PurchaseOrderDto> selectList(PurchaseOrderDto query) {
        List<PurchaseOrder> list = purchaseOrderMapper.selectByExample(buildCondition(query));
        List<PurchaseOrderDto> dtos = BeanConverter.copy(list, PurchaseOrderDto.class);
        for (PurchaseOrderDto dto : dtos) {
            packageDto(dto);
        }
        return dtos;
    }

    private PurchaseOrderExample buildCondition(PurchaseOrderDto query) {
        PurchaseOrderExample example = new PurchaseOrderExample();
        PurchaseOrderExample.Criteria criteria = example.createCriteria();
        criteria.andDeletedFlagEqualTo(DeletedFlag.VALID.code());

        if (query.getCreateBy() != null) {
            criteria.andCreateByEqualTo(query.getCreateBy());
        }
        if (ListUtil.isPresent(query.getIds())) {
            criteria.andIdIn(query.getIds());
        }
        Long receiptsId = query.getReceiptsId();
        if (Objects.nonNull(receiptsId)) {
            criteria.andReceiptsIdEqualTo(receiptsId);
        }
        return example;
    }

    @Override
    public PageInfo<PurchaseOrderDto> selectPage(PurchaseOrderDto query) {
        //数据权限
        final List<Long> ouList = SystemContext.getOus();
        if (ListUtils.isNotEmpty(ouList)) {
            query.setOuIdList(ouList);
        } else {
            ouList.add(NOT_EXIST_ID);
        }
        PageHelper.startPage(query.getPageNum(), query.getPageSize());
        List<PurchaseOrderDto> dtos = purchaseOrderExtMapper.selectListWithDetail(query);
        PageInfo<PurchaseOrderDto> page = BeanConverter.convertPage(dtos, PurchaseOrderDto.class);
        for (PurchaseOrderDto dto : page.getList()) {
            packageDto(dto);
        }
        return page;
    }

    private void packageDto(PurchaseOrderDto dto) {
        dto.setCurrencyCode(dto.getCurrency());
        if (dto.getOuId() != null) {
            OperatingUnit ou = CacheDataUtils.findOuById(dto.getOuId());
            if (ou != null) {
                dto.setProjectOuId(dto.getOuId());
                dto.setProjectOuName(ou.getOperatingUnitName());
            }
        }
        if (dto.getBuyerId() != null && dto.getBuyerName() == null) {
            UserInfo buyer = CacheDataUtils.findUserById(dto.getBuyerId());
            if (buyer != null && buyer.getName() != null) {
                dto.setBuyerName(buyer.getName());
            }
        }
        if (dto.getOrderStatus() != null) {
            dto.setStatusName(EnumUtil.getByCode(dto.getOrderStatus(), PurchaseOrderStatus.class));
        }
        if (dto.getSyncStatus() != null) {
            if (PurchaseOrderSyncStatus.NOT_SYNCED.code().equals(dto.getSyncStatus())) {
                dto.setSyncStatusName(PurchaseOrderSyncStatus.NOT_SYNCED.msg());
            } else if (PurchaseOrderSyncStatus.SYNCED.code().equals(dto.getSyncStatus())) {
                dto.setSyncStatusName(PurchaseOrderSyncStatus.SYNCED.msg());
            } else if (PurchaseOrderSyncStatus.FAILED_SYNC.code().equals(dto.getSyncStatus())) {
                dto.setSyncStatusName(PurchaseOrderSyncStatus.FAILED_SYNC.msg());
            }
        }
    }

    @Override
    public Boolean updateApproveInfoByPurchaseOrderReceiptsId(Long receiptsId, String approveInfo) {
        //更新--变更的记录
        PurchaseOrderChangeRecord purchaseOrderChangeRecord = new PurchaseOrderChangeRecord();
        purchaseOrderChangeRecord.setId(receiptsId);
        purchaseOrderChangeRecord.setApproveInfo(approveInfo);
        purchaseOrderChangeRecordMapper.updateByPrimaryKeySelective(purchaseOrderChangeRecord);
        //更新--原始的记录
        PurchaseOrderExample purchaseOrderExample = new PurchaseOrderExample();
        purchaseOrderExample.createCriteria().andReceiptsIdEqualTo(Long.valueOf(receiptsId));
        List<PurchaseOrder> purchaseOrderList = purchaseOrderMapper.selectByExample(purchaseOrderExample);
        if (ListUtils.isNotEmpty(purchaseOrderList)) {
            for (PurchaseOrder entity : purchaseOrderList) {
                PurchaseOrder purchaseOrder = new PurchaseOrder();
                purchaseOrder.setId(entity.getId());
                purchaseOrder.setApproveInfo(approveInfo);
                purchaseOrderMapper.updateByPrimaryKeySelective(purchaseOrder);
            }
        }
        return true;
    }

    @Override
    public List<PurchaseOrderChangeRecordDto> getVersionHistoryList(Long purchaseOrderId) {
        Asserts.notEmpty(purchaseOrderId, ErrorCode.ID_NOT_NULL);
        PurchaseOrder purchaseOrder = purchaseOrderMapper.selectByPrimaryKey(purchaseOrderId);
        Asserts.notEmpty(purchaseOrder, ErrorCode.CTC_ORDER_NOT_NULL);
        PurchaseOrderChangeRecordDto dto = new PurchaseOrderChangeRecordDto();
        List<PurchaseOrderChangeRecordDto> list = new ArrayList<>();
        String num = purchaseOrder.getNum();//订单编号
        dto.setId(purchaseOrderId);
        dto.setTag(0);//版本标识
        dto.setVersionCode(num + "-0");//版本号
        dto.setChangeType("订单新增");//订单版本更新类型
        dto.setCreateBy(purchaseOrder.getCreateBy());//设置提交人名字
        dto.setCreateAt(purchaseOrder.getCreateAt());//提交时间
        list.add(dto);
        List<PurchaseOrderChangeRecordDto> purchaseOrderChangeRecordDtoList = purchaseOrderExtMapper.getVersionHistoryList(purchaseOrderId);
        if (ListUtils.isNotEmpty(purchaseOrderChangeRecordDtoList)) {
            for (int i = 1; i <= purchaseOrderChangeRecordDtoList.size(); i++) {
                PurchaseOrderChangeRecordDto purchaseOrderChangeRecordDto = purchaseOrderChangeRecordDtoList.get(i - 1);
                //标识当前版本
                purchaseOrderChangeRecordDto.setTag(i);
                purchaseOrderChangeRecordDto.setVersionCode(num + "-" + i);//版本号
            }
            list.addAll(purchaseOrderChangeRecordDtoList);
        }
        return list;
    }

    @Override
    public PurchaseOrderPdfVO getVersionHistoryDetail(Long id, Integer tag, String approveInfo) {
        PurchaseOrderPdfVO baseVO = new PurchaseOrderPdfVO();
        //purchaseOrderId 如果是初始纪录则为采购订单号，不是，则为purchase_order_change_history.record_id
        //先查询purchase_order_change_history      和purchase_order_detail的数据
        //如果purchase_order_change_history中不存在，则该从未变更过，订单行数据可直接取purchase_order_detail
        //如果purchase_order_change_history中存在，则从purchase_order_detail_change_history取对应版本的数据且与purchase_order_detail的交集数据
        //统一过滤下单=取消的数据行
        if (Objects.equals(tag, 0)) {
            PurchaseOrder purchaseOrder = purchaseOrderMapper.selectByPrimaryKey(id);
            Asserts.notEmpty(purchaseOrder, ErrorCode.CTC_ORDER_NOT_NULL);
            extracted(tag, baseVO, purchaseOrder);
            //行数据
            List<Integer> statusList = new ArrayList<>();
            statusList.add(PurchaseOrderDetailStatus.ORDER_RELEASED.code());
            statusList.add(PurchaseOrderDetailStatus.ORDER_PLACED.code());
            statusList.add(PurchaseOrderDetailStatus.ORDER_CLOSED.code());
            PurchaseOrderDetailExample example = new PurchaseOrderDetailExample();
            example.createCriteria().andPurchaseOrderIdEqualTo(purchaseOrder.getId()).andRecordIdIsNull().andStatusIn(statusList).andDeletedFlagEqualTo(Boolean.FALSE);
            List<PurchaseOrderDetail> orderDetails = purchaseOrderDetailMapper.selectByExample(example);
            extracted(baseVO, purchaseOrder, orderDetails);
            baseVO.setVersionCreateTime(purchaseOrder.getCreateAt());
            //返回标准条款信息
            String contractTermsFlg = purchaseOrder.getContractTermsFlg();
            if (ContractTermsFlgEnum.STANDARD_TERMS.getCode().equals(contractTermsFlg)){
                PurchaseOrderStandardTermsExample purchaseOrderStandardTermsExample = new PurchaseOrderStandardTermsExample();
                PurchaseOrderStandardTermsExample.Criteria termsExampleCriteria = purchaseOrderStandardTermsExample.createCriteria();
                termsExampleCriteria.andDeletedFlagEqualTo(false).andPurchaseOrderIdEqualTo(id);
                List<PurchaseOrderStandardTerms> purchaseOrderStandardTerms = purchaseOrderStandardTermsMapper.selectByExampleWithBLOBs(purchaseOrderStandardTermsExample);
                if (CollectionUtils.isNotEmpty(purchaseOrderStandardTerms)) {
                    List<PurchaseOrderStandardTermsDto> purchaseOrderStandardTermsDtoList = new ArrayList<>();
                    purchaseOrderStandardTermsDtoListSave(purchaseOrderStandardTerms, purchaseOrderStandardTermsDtoList);
                    baseVO.setStandardTermsDtoList(purchaseOrderStandardTermsDtoList);
                }
            }else {
                String contractTerms = purchaseOrder.getContractTerms();
                baseVO.setContractTerms(contractTerms);
            }
            baseVO.setContractTermsFlg(purchaseOrder.getContractTermsFlg());

        } else {
            PurchaseOrderChangeHistory purchaseOrderChangeHistory = purchaseOrderChangeHistoryExtMapper.getPurchaseOrderChangeHistory(id);
            PurchaseOrder purchaseOrder = BeanConverter.copy(purchaseOrderChangeHistory, PurchaseOrder.class);
            extracted(tag, baseVO, purchaseOrder);
            //行数据
            List<Integer> statusList = new ArrayList<>();
            statusList.add(PurchaseOrderDetailStatus.ORDER_RELEASED.code());
            statusList.add(PurchaseOrderDetailStatus.ORDER_PLACED.code());
            statusList.add(PurchaseOrderDetailStatus.ORDER_CLOSED.code());
            PurchaseOrderDetailExample example = new PurchaseOrderDetailExample();
            example.createCriteria().andPurchaseOrderIdEqualTo(purchaseOrderChangeHistory.getOriginId()).andStatusIn(statusList).andDeletedFlagEqualTo(Boolean.FALSE);
            List<PurchaseOrderDetail> orderDetails = purchaseOrderDetailMapper.selectByExample(example);
            List<PurchaseOrderChangeRecordDto> versionHistoryList = getVersionHistoryList(purchaseOrderChangeHistory.getOriginId());

            //收集保留条件
            final List<Long> filterList = versionHistoryList.stream().filter(e -> e.getTag() <= tag).map(e -> e.getId()).collect(Collectors.toList());
            //与订单详情查到结果过滤
            List<PurchaseOrderDetail> newOrderDetails = orderDetails.stream()
                    .filter(entity -> Objects.isNull(entity.getRecordId()) || filterList.contains(entity.getRecordId())).collect(Collectors.toList());
            extracted(baseVO, purchaseOrder, newOrderDetails);

            versionHistoryList.stream().filter(e -> Objects.equals(e.getTag(), tag))
                    .findFirst()
                    .ifPresent(v->{baseVO.setVersionCreateTime(v.getCreateAt());});
            //返回标准条款信息
            PurchaseOrder purchaseOrder2 = purchaseOrderMapper.selectByPrimaryKey(purchaseOrderChangeHistory.getOriginId());
            String contractTermsFlg = purchaseOrder2.getContractTermsFlg();
            if (ContractTermsFlgEnum.STANDARD_TERMS.getCode().equals(contractTermsFlg)){
                PurchaseOrderStandardTermsExample purchaseOrderStandardTermsExample = new PurchaseOrderStandardTermsExample();
                PurchaseOrderStandardTermsExample.Criteria termsExampleCriteria = purchaseOrderStandardTermsExample.createCriteria();
                termsExampleCriteria.andDeletedFlagEqualTo(false).andPurchaseOrderIdEqualTo(purchaseOrderChangeHistory.getOriginId());
                List<PurchaseOrderStandardTerms> purchaseOrderStandardTerms = purchaseOrderStandardTermsMapper.selectByExampleWithBLOBs(purchaseOrderStandardTermsExample);
                if (CollectionUtils.isNotEmpty(purchaseOrderStandardTerms)) {
                    List<PurchaseOrderStandardTermsDto> purchaseOrderStandardTermsDtoList = new ArrayList<>();
                    purchaseOrderStandardTermsDtoListSave(purchaseOrderStandardTerms, purchaseOrderStandardTermsDtoList);
                    baseVO.setStandardTermsDtoList(purchaseOrderStandardTermsDtoList);
                }
            }else {
                String contractTerms = purchaseOrder2.getContractTerms();
                baseVO.setContractTerms(contractTerms);
            }
            baseVO.setContractTermsFlg(contractTermsFlg);

        }
        //实际下单数量 （数量为0，不体现）
        if (ListUtils.isNotEmpty(baseVO.getPurchaseOrderDetailPdfVOList())) {
            List<PurchaseOrderDetailPdfVO> collect =
                    baseVO.getPurchaseOrderDetailPdfVOList().stream().filter(e -> e.getOrderNum().compareTo(BigDecimal.ZERO) != 0).collect(Collectors.toList());
            Integer[] arrD = {1};
            List<PurchaseOrderDetailPdfVO> pList = collect.stream().peek(e -> e.setRow(arrD[0]++)).collect(Collectors.toList());
            baseVO.setPurchaseOrderDetailPdfVOList(pList);
        }
        //买方盖章
        PurchaseContractStampExample stampExample = new PurchaseContractStampExample();
        stampExample.createCriteria().andUnitIdEqualTo(SystemContext.getUnitId())
                .andStampTypeEqualTo(PurchaseContractStampType.UNIT_SEAL.getCode())
                .andDeletedFlagEqualTo(DeletedFlag.VALID.code());
        List<PurchaseContractStamp> unitSeals = purchaseContractStampMapper.selectByExample(stampExample);
        if (ListUtil.isPresent(unitSeals)) {
            baseVO.setAttachId(unitSeals.get(0).getAttachId());
        }

        //公司信息和收货地址
        setCompayAndDeliveryAddress(baseVO);

        //核准签名
        /** 变更时，如果金额增加了，取变更流程的审批人的签名图片，金额没有增加，取合同新建流程的审批人的签名图片 **/
        if (StringUtils.hasText(approveInfo)) {
            List<String> approveInfoList = Lists.newArrayList(approveInfo.split(","));
            List<String> mipNameList = new ArrayList<>();
            for (String item : approveInfoList) {
                String[] split = item.split("@");
                if (split.length > 1) {
                    String s = split[1];
                    if (s.contains(";")) {
                        String[] nameSplit = s.split(";");
                        List<String> nameSplitList = Stream.of(nameSplit).collect(Collectors.toList());
                        mipNameList.addAll(nameSplitList);
                    } else {
                        mipNameList.add(s);
                    }
                }
            }
            stampExample = new PurchaseContractStampExample();
            stampExample.createCriteria().andUnitIdEqualTo(SystemContext.getUnitId())
                    .andStampTypeEqualTo(PurchaseContractStampType.SIGNATURE.getCode())
                    .andMipNameIn(mipNameList)
                    .andDeletedFlagEqualTo(DeletedFlag.VALID.code());
            List<PurchaseContractStamp> signatures = purchaseContractStampMapper.selectByExample(stampExample);
            baseVO.setApproveInfoList(signatures);
        }



        return baseVO;
    }

    private void purchaseOrderStandardTermsDtoListSave(List<PurchaseOrderStandardTerms> purchaseOrderStandardTerms, List<PurchaseOrderStandardTermsDto> purchaseOrderStandardTermsDtoList) {
        for (PurchaseOrderStandardTerms purchaseOrderStandardTerm : purchaseOrderStandardTerms) {
            PurchaseOrderStandardTermsDto purchaseOrderStandardTermsDto = new PurchaseOrderStandardTermsDto();
            BeanUtils.copyProperties(purchaseOrderStandardTerm, purchaseOrderStandardTermsDto);

            //获取内容信息
//            PurchaseOrderStandardTermsContentExample purchaseOrderStandardTermsContentExample = new PurchaseOrderStandardTermsContentExample();
//            PurchaseOrderStandardTermsContentExample.Criteria contentExampleCriteria = purchaseOrderStandardTermsContentExample.createCriteria();
//            contentExampleCriteria.andDeletedFlagEqualTo(false).andAssociationPurchaseTermsIdEqualTo(purchaseOrderStandardTerm.getId());
//            List<PurchaseOrderStandardTermsContent> termsContentList = purchaseOrderStandardTermsContentMapper.selectByExample(purchaseOrderStandardTermsContentExample);
//            if (CollectionUtils.isNotEmpty(termsContentList)) {
//                purchaseOrderStandardTermsDto.setPurchaseOrderStandardTermsContentList(termsContentList);
//            }

            //获取偏离项信息
            StandardTermsDeviationExample standardTermsDeviationExample = new StandardTermsDeviationExample();
            StandardTermsDeviationExample.Criteria deviationExampleCriteria = standardTermsDeviationExample.createCriteria();
            deviationExampleCriteria.andDeletedFlagEqualTo(false).andAssociationPurchaseTermsIdEqualTo(purchaseOrderStandardTerm.getId());
            List<StandardTermsDeviation> standardTermsDeviations = standardTermsDeviationMapper.selectByExample(standardTermsDeviationExample);
            if (CollectionUtils.isNotEmpty(standardTermsDeviations)) {
                purchaseOrderStandardTermsDto.setStandardTermsDeviationList(standardTermsDeviations);
            }
            purchaseOrderStandardTermsDtoList.add(purchaseOrderStandardTermsDto);
        }
    }

    private void setCompayAndDeliveryAddress(PurchaseOrderPdfVO baseVO) {
        PurchaseContractStampExample companyStampExample = new PurchaseContractStampExample();
        companyStampExample.createCriteria().andUnitIdEqualTo(SystemContext.getUnitId())
                .andStampTypeEqualTo(PurchaseContractStampType.COMPANY.getCode())
                .andDeletedFlagEqualTo(DeletedFlag.VALID.code());
        List<PurchaseContractStamp> unitCompanyInfo = purchaseContractStampMapper.selectByExampleWithBLOBs(companyStampExample);
        if (ListUtil.isPresent(unitCompanyInfo)) {
            baseVO.setCompanyInfo(unitCompanyInfo.get(0).getCompanyInfo());
            baseVO.setDeliveryAddress(unitCompanyInfo.get(0).getDeliveryAddress());
        }
    }

    /**
     * 根据物料编码列表和库存组织ID列表获取物料信息
     *
     * @param erpCodes 物料编码列表
     * @param organizationIds 库存组织ID列表
     * @return 物料编码到物料信息的映射
     */
    private Map<String, MaterialDto> getMaterialInfoByErpCodeListAndOrgIdList(List<String> erpCodes, List<Long> organizationIds) {
        if (CollectionUtils.isEmpty(erpCodes) || CollectionUtils.isEmpty(organizationIds)) {
            logger.warn("物料编码列表或库存组织ID列表为空，无法获取物料信息");
            return Collections.emptyMap();
        }

        try {
            // 构建请求参数
            MaterialQueryDTO materialQueryDTO = new MaterialQueryDTO();
            materialQueryDTO.setErpCodeList(erpCodes);
            materialQueryDTO.setOrganizationIdList(organizationIds);

            // 构建请求URL
            String url = String.format("%smaterial/getByErpCodeListAndOrgIdList", ModelsEnum.BASEDATA.getBaseUrl());

            // 发起HTTP POST请求
            ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, materialQueryDTO, String.class);
            String response = responseEntity.getBody();

            if (StringUtils.hasText(response)) {
                // 解析响应结果
                DataResponse<List<MaterialDto>> dataResponse = JSON.parseObject(response,
                    new TypeReference<DataResponse<List<MaterialDto>>>() {});

                if (dataResponse != null && dataResponse.getCode() == 0 && CollectionUtils.isNotEmpty(dataResponse.getData())) {
                    // 转换为Map，以物料编码为key
                    Map<String, MaterialDto> materialMap = dataResponse.getData().stream()
                            .filter(material -> StringUtils.hasText(material.getItemCode()))
                            .collect(Collectors.toMap(
                                MaterialDto::getItemCode,
                                Function.identity(),
                                (existing, replacement) -> existing // 如果有重复key，保留第一个
                            ));

                    logger.info("根据物料编码列表和库存组织ID列表获取到{}个物料信息", materialMap.size());
                    return materialMap;
                } else {
                    logger.warn("获取物料信息失败或无数据, erpCodes={}, organizationIds={}, response={}",
                        erpCodes, organizationIds, response);
                    return Collections.emptyMap();
                }
            } else {
                logger.warn("获取物料信息响应为空, erpCodes={}, organizationIds={}", erpCodes, organizationIds);
                return Collections.emptyMap();
            }
        } catch (Exception e) {
            logger.error("获取物料信息异常, erpCodes={}, organizationIds={}, 异常信息: {}",
                erpCodes, organizationIds, e.getMessage(), e);
            return Collections.emptyMap();
        }
    }

    private void extracted(PurchaseOrderPdfVO baseVO, PurchaseOrder purchaseOrder, List<PurchaseOrderDetail> orderDetails) {
        Asserts.notEmpty(orderDetails, ErrorCode.CTC_ORDER_DETAILS_NOT_NULL);
        orderDetails.stream()
                .filter(e -> ObjectUtil.isNotEmpty(e.getCancelNum()))
                .forEach(e -> {
                    e.setOrderNum(e.getOrderNum().subtract(e.getCancelNum()));
                    e.setDiscountMoney(Optional.ofNullable(e.getDiscountPrice()).orElse(BigDecimal.ZERO).multiply(e.getOrderNum()).setScale(2,
                            BigDecimal.ROUND_HALF_UP));
                });
        BigDecimal excludingTaxAmount =
                orderDetails.stream().map(e -> Optional.ofNullable(e.getDiscountMoney()).orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO,
                        BigDecimal::add);
        //不含税总价
        baseVO.setExcludingTaxAmount(excludingTaxAmount.setScale(2, BigDecimal.ROUND_HALF_UP));
        //税
        BigDecimal taxRate = new BigDecimal(BigInteger.ZERO);
        if (org.apache.commons.lang3.StringUtils.isNotBlank(purchaseOrder.getTaxRate()) && org.apache.commons.lang3.StringUtils.isNumeric(purchaseOrder.getTaxRate().replaceAll("%", ""))) {
            taxRate = new BigDecimal(purchaseOrder.getTaxRate().replaceAll("%", ""));
        }
        baseVO.setTax(excludingTaxAmount.multiply(taxRate).divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP));
        //总价
        BigDecimal amount = excludingTaxAmount.add(baseVO.getTax());
        baseVO.setAmount(amount.setScale(2, BigDecimal.ROUND_HALF_UP));

        List<PurchaseOrderDetailPdfVO> purchaseOrderDetailPdfVOList = BeanConverter.copy(orderDetails, PurchaseOrderDetailPdfVO.class);

        //查询物料备注
        Long purchaseOrderOuId = purchaseOrder.getOuId();

        // 根据ouId获取库存组织ID
        OperatingUnitDto operatingUnit = CacheDataUtils.findOuById(purchaseOrderOuId);

        if(Objects.nonNull(operatingUnit)){
            Long organizationId = operatingUnit.getOrganizationId();

            // 提取订单明细中的物料编码列表
            List<String> erpCodes = orderDetails.stream()
                    .map(PurchaseOrderDetail::getErpCode)
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(erpCodes)) {
                // 调用MaterialController#getByErpCodeListAndOrgIdList获取物料信息
                Map<String, MaterialDto> materialMap = getMaterialInfoByErpCodeListAndOrgIdList(erpCodes, Lists.newArrayList(organizationId));

                // 将物料信息设置到PDF VO中
                if (CollectionUtils.isNotEmpty(purchaseOrderDetailPdfVOList)) {
                    for (PurchaseOrderDetailPdfVO pdfVO : purchaseOrderDetailPdfVOList) {
                        if (StringUtils.hasText(pdfVO.getErpCode())) {
                            MaterialDto materialDto = materialMap.get(pdfVO.getErpCode());
                            if (materialDto != null) {
                                // 设置物料备注等信息
                                pdfVO.setMaterialRemark(materialDto.getRemark());
                            }
                        }
                    }
                }
            }
        }

        baseVO.setPurchaseOrderDetailPdfVOList(purchaseOrderDetailPdfVOList);
    }

    private void extracted(Integer tag, PurchaseOrderPdfVO baseVO, PurchaseOrder purchaseOrder) {
        //采购订单编号
        baseVO.setContractCode(purchaseOrder.getNum());
        //合同版本号
        baseVO.setVersionCode(purchaseOrder.getNum() + "-" + tag);//版本号
        //使用单位名称
        final Unit unit = CacheDataUtils.findUnitById(SystemContext.getUnitId());
        if (unit != null) {
            baseVO.setUnitName(unit.getUnitName());
        }
        //合同日期
        baseVO.setContractDate(new Date());
        //卖方
        baseVO.setVendorName(purchaseOrder.getVendorNum() + " " + purchaseOrder.getVendorName());
        //买方
        baseVO.setBuyerName(purchaseOrder.getBuyerName());
        //买方邮箱
        if (purchaseOrder.getBuyerId() != null) {
            final UserInfo userInfo = CacheDataUtils.findUserById(purchaseOrder.getBuyerId());
            if (userInfo != null) {
                baseVO.setBuyerNameEmail(Optional.ofNullable(userInfo.getOriginalMail()).orElse(userInfo.getEmail()));
            }
        }
        //币种
        baseVO.setCurrency(purchaseOrder.getCurrency());
        //付款条件/方法
        baseVO.setPaymentConditionAndWay(purchaseOrder.getPaymentWay() + "/" + purchaseOrder.getPaymentMethodName());
        //交货方式
        baseVO.setDeliveryType(purchaseOrder.getDeliveryType());
        //交货条款
        baseVO.setDeliveryClause(purchaseOrder.getDeliveryClause());
        //合同条款
        baseVO.setContractTerms(purchaseOrder.getContractTerms());
    }

    @Override
    public String getDdInstanceId(Long id, Integer tag) {
        //暂只处理tag=0的数据
        return purchaseOrderExtMapper.getDdInstanceId(id);
    }

    @Override
    public List<PurchaseOrderImportVO> importPurchaseOrder(List<PurchaseOrderImportVO> excelVoList) {
        // 分批导入
        List<PurchaseOrderImportVO> resultList = new ArrayList<>();
        List<List<PurchaseOrderImportVO>> lists = ListUtils.splistList(excelVoList, 200);
        for (List<PurchaseOrderImportVO> splitList : lists) {
            resultList.addAll(batchImportPurchaseOrder(splitList));
        }
        return resultList;
    }

    private Collection<? extends PurchaseOrderImportVO> batchImportPurchaseOrder(List<PurchaseOrderImportVO> excelVoList) {
        boolean isPass = validPurchaseOrderExcelVo(excelVoList);

        if (!isPass) {
            return excelVoList.stream().filter(e -> StringUtils.isNotEmpty(e.getErrMsg())).collect(Collectors.toList());
        }
        List<PurchaseOrder> purchaseOrderList = new ArrayList<>();
        excelVoList.forEach(vo -> {
            PurchaseOrder purchaseOrder = BeanConverter.copy(vo, PurchaseOrder.class);
            purchaseOrder.setDeletedFlag(Boolean.FALSE);
            purchaseOrder.setOrderStatus(PurchaseOrderStatus.APPROVED.code());
            purchaseOrder.setSource("2");// 来源:非wbs:1;wbs:2

            if (purchaseOrder.getId() == null) {
                purchaseOrder.setCreateBy(Long.parseLong(DateUtils.format(new Date(), "yyyyMMdd")));
                purchaseOrder.setCreateAt(new Date());
                purchaseOrder.setApprovalTime(new Date());
                purchaseOrderList.add(purchaseOrder);
            } else {
                purchaseOrder.setUpdateBy(Long.parseLong(DateUtils.format(new Date(), "yyyyMMdd")));
                purchaseOrder.setUpdateAt(new Date());
                purchaseOrderMapper.updateByPrimaryKeySelective(purchaseOrder);
            }
        });
        if (ListUtils.isNotEmpty(purchaseOrderList)) {
            purchaseOrderExtMapper.batchInsert(purchaseOrderList);
        }
        return Collections.emptyList();
    }

    /**
     * 校验导入的采购订单头数据
     *
     * @param excelVoList 采购订单头数据集合
     * @return 校验通过返回true
     */
    private boolean validPurchaseOrderExcelVo(List<PurchaseOrderImportVO> excelVoList) {

        excelVoList.forEach(c -> c.setErrMsg(validator.validate(c).stream()
                .map(ConstraintViolation::getMessage).collect(Collectors.joining(","))));

        if (excelVoList.stream().anyMatch(c -> StringUtils.isNotEmpty(c.getErrMsg()))) {
            return false;
        }

        List<String> buyerIdList = excelVoList.stream().map(o -> o.getBuyerId() + "")
                .distinct().collect(Collectors.toList());
        Set<String> usernames = new HashSet<>(buyerIdList);
        Map<String, UserInfoDto> userMap = getUserByUserNumber(usernames).stream()
                .collect(Collectors.toMap(UserInfoDto::getEmployeeNumber, e -> e, (e1, e2) -> e1));

        List<String> ouNames = excelVoList.stream().map(PurchaseOrderImportVO::getOrgName)
                .distinct().collect(Collectors.toList());
        Map<String, OrganizationRelDto> organizationRelMap = getOrganizationRelByOuName(ouNames).stream()
                .collect(Collectors.toMap(OrganizationRelDto::getOperatingUnitName, e -> e, (e1, e2) -> e1));

        List<String> getCodes = excelVoList.stream().map(PurchaseOrderImportVO::getNum).distinct().collect(Collectors.toList());
        PurchaseOrderExample purchaseOrderExample = new PurchaseOrderExample();
        purchaseOrderExample.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andNumIn(getCodes);
        Map<String, Long> purchaseOrderMap = purchaseOrderMapper.selectByExample(purchaseOrderExample).stream()
                .collect(Collectors.toMap(PurchaseOrder::getNum, PurchaseOrder::getId));

        List<String> vendorCodes = excelVoList.stream().map(PurchaseOrderImportVO::getVendorNum).collect(Collectors.toList());
        Map<String, List<VendorSiteBankDto>> vendorSiteBankGroup = getVendorSiteBankByCodes(vendorCodes).stream()
                .collect(Collectors.groupingBy(VendorSiteBankDto::getVendorCode));

        excelVoList.forEach(vo -> {
            List<String> errMsgs = new ArrayList<>();

            vo.setDispatchIs(Boolean.FALSE);
            vo.setSyncStatus(PurchaseOrderSyncStatus.SYNCED.code());

            if (Objects.equals(vo.getDispatchIsStr(), "是")) {
                vo.setDispatchIs(Boolean.TRUE);
            }
            vo.setId(purchaseOrderMap.get(vo.getNum()));

            if (Objects.equals(vo.getPricingTypeStr(), "一单一价")) {
                vo.setPricingType(PricingTypeEnum.A_SINGLE_PRICE.code());
            } else if (Objects.equals(vo.getPricingTypeStr(), "一揽子协议")) {
                vo.setPricingType(PricingTypeEnum.PACKAGE_AGREEMENT.code());
            } else {
                errMsgs.add("定价类型有误");
            }

            UserInfoDto user = userMap.get(vo.getBuyerId() + "");
            if (user != null) {
                vo.setBuyerName(user.getName());
                vo.setBuyerId(user.getId());
                vo.setApproveInfo("N1@" + user.getUsername());
            } else {
                errMsgs.add("采购员工号不存在");
            }

            OrganizationRelDto orgRel = organizationRelMap.get(vo.getOrgName());
            if (orgRel != null) {
                vo.setOrgId(orgRel.getOrganizationId());
                vo.setOuId(orgRel.getOperatingUnitId());

                List<VendorSiteBankDto> vendorSiteBankDtos = vendorSiteBankGroup.get(vo.getVendorNum());
                if (ListUtils.isNotEmpty(vendorSiteBankDtos)) {
                    Optional<VendorSiteBankDto> first = vendorSiteBankDtos.stream().filter(e ->
                            orgRel.getOperatingUnitId().equals(e.getOperatingUnitId()) &&
                                    vo.getVendorName().equals(e.getVendorName()) &&
                                    vo.getVendorSiteCode().equals(e.getVendorSiteCode())).findFirst();
                    if (first.isPresent()) {
                        vo.setVendorAslId(first.get().getId());
                    } else {
                        errMsgs.add(vo.getVendorNum() + "供应商数据不匹配");
                    }
                } else {
                    errMsgs.add(vo.getVendorNum() + "供应商不存在");
                }
            } else {
                errMsgs.add("业务实体不存在");
            }

            vo.setErrMsg(String.join(",", errMsgs));
        });

        return excelVoList.stream().allMatch(e -> StringUtils.isEmpty(e.getErrMsg()));
    }

    @Override
    public List<PurchaseOrderDetailImportVO> importPurchaseOrderDetail(List<PurchaseOrderDetailImportVO> excelVoList, Long unitId) {
        // 分批导入
        List<PurchaseOrderDetailImportVO> resultList = new ArrayList<>();
        List<List<PurchaseOrderDetailImportVO>> lists = ListUtils.splistList(excelVoList, 200);
        for (List<PurchaseOrderDetailImportVO> splitList : lists) {
            resultList.addAll(batchImportPurchaseOrderDetail(splitList, unitId));
        }
        return resultList;
    }

    private Collection<? extends PurchaseOrderDetailImportVO> batchImportPurchaseOrderDetail(List<PurchaseOrderDetailImportVO> excelVoList,
                                                                                             Long unitId) {
        unitId = unitId != null ? unitId : SystemContext.getUnitId();
        boolean isPass = validPurchaseOrderDetailExcelVo(excelVoList, unitId);
        if (isPass) {
            List<PurchaseOrderDetail> purchaseOrderDetailList = new ArrayList<>();
            excelVoList.forEach(vo -> {
                PurchaseOrderDetail orderDetail = BeanConverter.copy(vo, PurchaseOrderDetail.class);
                orderDetail.setCreateBy(Long.parseLong(DateUtils.format(new Date(), "yyyyMMdd")));
                orderDetail.setCreateAt(new Date());
                orderDetail.setDeletedFlag(Boolean.FALSE);
                purchaseOrderDetailList.add(orderDetail);
            });
            purchaseOrderDetailExtMapper.batchInsert(purchaseOrderDetailList);
            List<Long> purchaseOrderIdList = purchaseOrderDetailList.stream().map(PurchaseOrderDetail::getPurchaseOrderId)
                    .distinct().filter(Objects::nonNull).collect(Collectors.toList());
            purchaseOrderIdList.add(-1L);
            purchaseOrderDetailExtMapper.batchUpdatePurchaseOrder(purchaseOrderIdList);
            return Collections.emptyList();
        }
        return excelVoList.stream().filter(e -> StringUtils.isNotEmpty(e.getErrMsg())).collect(Collectors.toList());
    }

    /**
     * 兼容wbs和非wbs的采购需求下达数量统计（即采购订单汇总）
     *
     * @param projectId
     * @return
     */
    @Override
    public Map<String, BigDecimal> queryOrderNumMapByProjectId(Long projectId) {
        Map<String, BigDecimal> orderNumMap = new HashMap<>();
        PurchaseMaterialRequirementDto requirementQuery = new PurchaseMaterialRequirementDto();
        requirementQuery.setProjectId(projectId);
        // 统计采购订单行数量
        List<PurchaseMaterialRequirementDto> requirementDtos =
                purchaseMaterialRequirementExtMapper.queryOrderNumByProjectId(requirementQuery);
        // 统计采购订单合并行数量
        requirementDtos.addAll(purchaseMaterialRequirementExtMapper.queryOrderMergeNumByProjectId(requirementQuery));
        // 统计采购订单行变更数量
        requirementDtos.addAll(purchaseMaterialRequirementExtMapper.queryOrderHistoryNumByProjectId(requirementQuery));
        //统计采购订单合并行变更数量
        requirementDtos.addAll(purchaseMaterialRequirementExtMapper.queryOrderMergeHistoryNumByProjectId(requirementQuery));

        if (ListUtil.isPresent(requirementDtos)) {
            // 按照erpcode+DeliveryTime取下达量，并汇总releasedQuantity数量
            orderNumMap = requirementDtos.stream().collect(Collectors.groupingBy(e -> e.getErpCode()
                            + "-" + e.getWbsSummaryCode()
                            + "-" + DateUtils.format(e.getDeliveryTime(), DateUtils.FORMAT_SHORT),
                    Collectors.reducing(BigDecimal.ZERO, PurchaseMaterialRequirementDto::getReleasedQuantity, BigDecimal::add)));
        }
        return orderNumMap;
    }

    @Override
    public ResponseMap getMobileApprovalDetail(Long receiptsId) {
        // 移动审批模板对象
        ResponseMap responseMap = new ResponseMap();
        // 查询详情,跟pc端数据保持一致
        SaveBatchPurchaseOrderRecordDto details = getDetails(receiptsId, null);
        if (Objects.isNull(details) || CollectionUtil.isEmpty(details.getPurchaseOrderReceiptsDtoList())) {
            responseMap.setStatus("fail");
            responseMap.setMsg("未查询到采购订单详情内容");
            return responseMap;
        }
        for (PurchaseOrderReceiptsDto receiptsDto : details.getPurchaseOrderReceiptsDtoList()) {
            PurchaseOrderDto purchaseOrderDto = receiptsDto.getPurchaseOrderDto();
            // 采购订单基本信息
            Map<String, String> headMap = Maps.newHashMap();
            headMap.put("num", purchaseOrderDto.getNum());
            headMap.put("pricingType", PricingTypeEnum.getValue(purchaseOrderDto.getPricingType()));
            String dispatchIs = BooleanUtil.isTrue(purchaseOrderDto.getDispatchIs()) ? "是" : "否";
            headMap.put("dispatchIs", dispatchIs);
            headMap.put("buyerName", purchaseOrderDto.getBuyerName());
            headMap.put("erpBuyerName", getErpBuyerName(purchaseOrderDto.getErpBuyerId()));
            headMap.put("vendorCode", purchaseOrderDto.getVendorNum());
            headMap.put("vendorName", purchaseOrderDto.getVendorName());
            headMap.put("vendorSiteCode", purchaseOrderDto.getVendorSiteCode());
            headMap.put("currencyCode", purchaseOrderDto.getCurrency());
            headMap.put("conversionType", purchaseOrderDto.getConversionType());
            headMap.put("conversionRate", String.valueOf(purchaseOrderDto.getConversionRate()));
            String conversionDate = DateUtils.format(purchaseOrderDto.getConversionDate(), DateUtils.FORMAT_SHORT);
            headMap.put("conversionDate", conversionDate);
            headMap.put("taxRate", purchaseOrderDto.getTaxRate());
            headMap.put("paymentMethodName", purchaseOrderDto.getPaymentMethodName());
            headMap.put("paymentWay", purchaseOrderDto.getPaymentWay());
            headMap.put("deliveryType", purchaseOrderDto.getDeliveryType());
            headMap.put("deliveryClause", purchaseOrderDto.getDeliveryClause());
            headMap.put("contractTermsDetail", "详见附件合同条款");
            headMap.put("orderDetail", "详见附件订单明细");
            headMap.put("operatingUnitName", purchaseOrderDto.getOrgName());
            headMap.put("remark", purchaseOrderDto.getRemark());
            responseMap.setHeadMap(headMap);

            List<Map<String, String>> list1 = new ArrayList<>();
            Map<String, String> listMap = Maps.newHashMap();
            // 预算信息
            List<PurchaseOrderDetailDto> detailDtoList = receiptsDto.getPurchaseOrderDetailDtoList();
            for (PurchaseOrderDetailDto remain : detailDtoList) {
                listMap.put("requirementCode", remain.getRequirementCode());
                listMap.put("wbsSummaryCode", remain.getWbsSummaryCode());
                // 增加 预算占用金额
                listMap.put("budgetOccupiedAmount", remain.getBudgetOccupiedAmount() == null ? "0" :
                        remain.getBudgetOccupiedAmount().stripTrailingZeros().toPlainString());
                BigDecimal discountMoney = remain.getDiscountMoney();
                // 从订单头取汇率
                BigDecimal conversionRate = Optional.ofNullable(purchaseOrderDto.getConversionRate()).orElse(new BigDecimal(1));
                discountMoney = discountMoney.multiply(conversionRate);
                listMap.put("discountMoney", String.valueOf(discountMoney));
                list1.add(listMap);
            }
            responseMap.setList1(list1);

            //标准条款信息
            if (ContractTermsFlgEnum.STANDARD_TERMS.getCode().equals(purchaseOrderDto.getContractTermsFlg())) {
                PurchaseOrderStandardTermsExample purchaseOrderStandardTermsExample = new PurchaseOrderStandardTermsExample();
                PurchaseOrderStandardTermsExample.Criteria termsExampleCriteria = purchaseOrderStandardTermsExample.createCriteria();
                termsExampleCriteria.andDeletedFlagEqualTo(false).andPurchaseOrderIdEqualTo(purchaseOrderDto.getId());
                List<PurchaseOrderStandardTerms> purchaseOrderStandardTerms = purchaseOrderStandardTermsMapper.selectByExampleWithBLOBs(purchaseOrderStandardTermsExample);
                List<Map<String, String>> list2 = new ArrayList<>();
                for (PurchaseOrderStandardTerms purchaseOrderStandardTerm : purchaseOrderStandardTerms) {
                    // 每次循环创建新的Map
                    Map<String, String> standardTermsListMap = new HashMap<>();
                    standardTermsListMap.put("termsCode", purchaseOrderStandardTerm.getTermsCode());
                    standardTermsListMap.put("termsName", purchaseOrderStandardTerm.getTermsName());
                    standardTermsListMap.put("termsDisplayContent", purchaseOrderStandardTerm.getTermsDisplayContent());

                    // 处理偏离项
                    StandardTermsDeviationExample deviationExample = new StandardTermsDeviationExample();
                    deviationExample.createCriteria()
                            .andDeletedFlagEqualTo(false)
                            .andAssociationPurchaseTermsIdEqualTo(purchaseOrderStandardTerm.getId());
                    List<StandardTermsDeviation> deviations = standardTermsDeviationMapper.selectByExample(deviationExample);
                    StringBuilder deviationBuilder = new StringBuilder();
                    if (CollectionUtils.isNotEmpty(deviations)) {
                        int count = 1;
                        for (StandardTermsDeviation deviation : deviations) {
                            if (StringUtils.isNotEmpty(deviation.getDeviationInfo())) {
                                if (count > 1) deviationBuilder.append("\n");
                                deviationBuilder.append("偏离项").append(count++).append("：").append(deviation.getDeviationInfo());
                            }
                        }
                    }

                    standardTermsListMap.put("standardTermsDeviation", deviationBuilder.toString());
                    list2.add(standardTermsListMap);
                }
                responseMap.setList2(list2);
            }
        }

        CtcAttachmentDto attachmentQuery = new CtcAttachmentDto();
        attachmentQuery.setModule(CtcAttachmentModule.PURCHASE_ORDER.code());
        attachmentQuery.setModuleId(receiptsId);
        // 查询附件(包含移动端与pc端)
        List<CtcAttachmentDto> attachmentList = ctcAttachmentService.selectList(attachmentQuery);
        if (CollectionUtil.isNotEmpty(attachmentList)) {
            List<AduitAtta> fileList = new ArrayList<>();
            attachmentList.forEach(attachment -> {
                AduitAtta aduitAtta = new AduitAtta();
                aduitAtta.setFdId(String.valueOf(attachment.getAttachId()));
                String fileName = attachment.getFileName() == null ? attachment.getAttachName() : attachment.getFileName();
                aduitAtta.setFileName(fileName);
                aduitAtta.setFileSize(String.valueOf(attachment.getFileSize()));
                fileList.add(aduitAtta);
            });
            responseMap.setFileList(fileList);
        }
        responseMap.setStatus("success");
        return responseMap;
    }

    @Override
    public List<PurchaseOrderDto> getOrderByReceiptsId(Long receiptsId) {
        PurchaseOrderExample purchaseOrderExample = new PurchaseOrderExample();
        purchaseOrderExample.createCriteria()
                .andReceiptsIdEqualTo(receiptsId)
                .andDeletedFlagEqualTo(Boolean.FALSE);
        List<PurchaseOrder> purchaseOrderList = purchaseOrderMapper.selectByExample(purchaseOrderExample);
        if (CollectionUtil.isEmpty(purchaseOrderList)) {
            return Lists.newArrayList();
        }
        List<PurchaseOrderDto> purchaseOrderDtos = BeanConverter.copy(purchaseOrderList, PurchaseOrderDto.class);
        Map<Long, String> erpBuyerNames = new HashMap<>();
        purchaseOrderDtos.forEach(e ->
                e.setErpBuyerName(erpBuyerNames.computeIfAbsent(e.getErpBuyerId(), this::getErpBuyerName))
        );
        return purchaseOrderDtos;
    }

    private boolean validPurchaseOrderDetailExcelVo(List<PurchaseOrderDetailImportVO> excelVoList, Long unitId) {
        excelVoList.forEach(c -> c.setErrMsg(validator.validate(c).stream()
                .map(ConstraintViolation::getMessage).collect(Collectors.joining(","))));

        if (excelVoList.stream().anyMatch(c -> org.apache.commons.lang3.StringUtils.isNotEmpty(c.getErrMsg()))) {
            return false;
        }

        List<String> projectCodes = excelVoList.stream().map(PurchaseOrderDetailImportVO::getProjectNum).distinct().collect(Collectors.toList());
        ProjectExample projectExample = new ProjectExample();
        projectExample.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andCodeIn(projectCodes);
        Map<String, Project> projectMap = projectMapper.selectByExample(projectExample).stream()
                .collect(Collectors.toMap(Project::getCode, e -> e, (e1, e2) -> e1));

        List<Long> projectIdList = projectMap.values().stream().map(Project::getId).collect(Collectors.toList());
        ProjectProfitExample profitExample = new ProjectProfitExample();
        profitExample.createCriteria().andProjectIdIn(projectIdList).andDeletedFlagEqualTo(DeletedFlag.VALID.code());
        Map<Long, Long> organizationIdMap =
                projectProfitMapper.selectByExample(profitExample).stream().collect(Collectors.toMap(ProjectProfit::getProjectId,
                        ProjectProfit::getStorageId, (a, b) -> a));

        List<String> getCodes = excelVoList.stream().map(PurchaseOrderDetailImportVO::getOrderNo).distinct().collect(Collectors.toList());
        PurchaseOrderExample purchaseOrderExample = new PurchaseOrderExample();
        purchaseOrderExample.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andNumIn(getCodes);
        List<PurchaseOrder> purchaseOrderList = purchaseOrderMapper.selectByExample(purchaseOrderExample);
        Map<String, PurchaseOrder> purchaseOrderMap = purchaseOrderList.stream()
                .collect(Collectors.toMap(PurchaseOrder::getNum, e -> e, (e1, e2) -> e1));

        List<String> pamCodes = excelVoList.stream().map(PurchaseOrderDetailImportVO::getPamCode).distinct().collect(Collectors.toList());
//        List<Long> orgIds = purchaseOrderList.stream().map(PurchaseOrder::getOrgId).distinct().collect(Collectors.toList());
        List<Material> pamCodeMaterialList = materialExtService.invokeMaterialApiGetByPamCodeListAndOrgIdList(pamCodes,
                new ArrayList<>(organizationIdMap.values()));
        Map<String, Material> pamCodeMaterialMap = CollectionUtils.isEmpty(pamCodeMaterialList) ? new HashMap<>()
                : pamCodeMaterialList.stream().collect(Collectors.toMap(e -> getPamCodeMaterialKey(e.getPamCode(), e.getOrganizationId()),
                Function.identity()));

        excelVoList.forEach(vo -> {
            List<String> errMsgs = new ArrayList<>();
            vo.setOrderNum(vo.getQuantity());
            vo.setDeliveredQuantity(BigDecimal.ZERO);
            vo.setCancelNum(BigDecimal.ZERO);
            vo.setCost(vo.getUnitPrice());
            vo.setMergeRows(Boolean.FALSE);
            vo.setBudgetOccupiedAmount(BigDecimal.ZERO);
            vo.setRequirementCodeStar(0);
            vo.setWbsSummaryCodeStar(0);
            vo.setDesignReleaseLotNumberStar(0);
            vo.setStatus(PurchaseOrderDetailStatus.ORDER_PLACED.code());
            if (vo.getDeliveryTime() != null) {
                vo.setNeedByDate(DateUtils.formatDate(vo.getDeliveryTime()));
            }

            Project project = projectMap.get(vo.getProjectNum());
            if (project != null) {
                vo.setProjectId(project.getId());
                vo.setProjectName(project.getName());
                vo.setProjectCode(vo.getProjectNum());
                vo.setOrganizationId(organizationIdMap.get(project.getId()));
            } else {
                errMsgs.add("项目编号不存在");
            }

            PurchaseOrder purchaseOrder = purchaseOrderMap.get(vo.getOrderNo());
            if (purchaseOrder != null) {
                vo.setPurchaseOrderId(purchaseOrder.getId());
                vo.setVendorName(purchaseOrder.getVendorName());
                vo.setVendorAslId(purchaseOrder.getVendorAslId());
                vo.setVendorSiteCode(purchaseOrder.getVendorSiteCode());
                vo.setVendorNum(purchaseOrder.getVendorNum());

                Material material = pamCodeMaterialMap.get(getPamCodeMaterialKey(vo.getPamCode(), vo.getOrganizationId()));
                if (material != null) {
                    vo.setMaterielId(material.getId());
                    vo.setMaterielDescr(material.getItemInfo());
                    vo.setUnitCode(material.getUnit());
                    if (StringUtils.isNotEmpty(material.getUnit())) {
                        DictDto unit = CacheDataUtils.findDictByTypeAndCode(DictType.MEASUREMENT_UNIT.code(), material.getUnit());
                        if (unit != null) {
                            vo.setUnit(unit.getName());
                        }
                    }
                    vo.setBrand(material.getBrand());
                    vo.setModel(material.getModel());
                    vo.setChartVersion(material.getChartVersion());
                } else {
                    errMsgs.add("物料编码不存在");
                }
            } else {
                errMsgs.add("采购订单编号不存在");
            }

            vo.setErrMsg(String.join(",", errMsgs));
        });

        return excelVoList.stream().allMatch(e -> org.apache.commons.lang3.StringUtils.isEmpty(e.getErrMsg()));
    }

    @Override
    public List<PurchaseOrderDto> getPendingOrder(Long buyerBy) throws Exception {
        Guard.notNull(buyerBy, "采购员信息不能为空");

        //查找组织参数：采购订单分组规则
        Set<String> purchaseOrderGroupRuleSet = organizationCustomDictService.queryByName(Constants.PURCHASE_ORDER_GROUP_RULE,
                SystemContext.getUnitId(), OrgCustomDictOrgFrom.COMPANY);
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(purchaseOrderGroupRuleSet) || !(purchaseOrderGroupRuleSet.contains("1") || purchaseOrderGroupRuleSet.contains("2"))) {
            return new ArrayList<>();
        }

        //查找组织参数：是否查询控制一揽子协议
        final OrgCustomDictOrgFrom orgCustomDictOrgFrom = OrgCustomDictOrgFrom.getOrgCustomDictOrgFrom("company");
        if (orgCustomDictOrgFrom == null) {
            throw new MipException("类型参数有误");
        }
        List<OrganizationCustomDict> dicts = organizationCustomDictService.queryByOrdId(SystemContext.getUnitId(), "无定价是否允许下采购订单",
                orgCustomDictOrgFrom);

        //获取采购员对应所有的已下达的订单明细
        PurchaseOrderDetailExample example = new PurchaseOrderDetailExample();
        example.createCriteria().andCreateByEqualTo(buyerBy).andProjectIdIsNotNull().andVendorAslIdIsNotNull()
                .andStatusEqualTo(PurchaseOrderDetailStatus.ORDER_RELEASED.code()).andDeletedFlagEqualTo(DeletedFlag.VALID.code());
        List<PurchaseOrderDetailDto> orderDetailDtos = BeanConverter.copy(purchaseOrderDetailMapper.selectByExample(example),
                PurchaseOrderDetailDto.class);
        if (CollectionUtils.isEmpty(orderDetailDtos)) {
            return new ArrayList<>();
        }

        List<Long> requirementIds = new ArrayList<>();
        List<Long> projectIdList = new ArrayList<>();
        for (PurchaseOrderDetailDto purchaseOrderDetailDto : orderDetailDtos) {
            Long materialPurchaseRequirementId = purchaseOrderDetailDto.getMaterialPurchaseRequirementId();
            if (null != materialPurchaseRequirementId) {
                requirementIds.add(materialPurchaseRequirementId);
            }
            projectIdList.add(purchaseOrderDetailDto.getProjectId());
        }

        Map<Long, List<PurchaseMaterialRequirementDto>> requirementMap = new HashMap<>();
        Map<Long, Date> publishTimeMap = new HashMap<>();
        if (ListUtils.isNotEmpty(requirementIds)) {
            //查询当前订单明细的物料对应采购需求未下达量
            PurchaseMaterialRequirementDto query = new PurchaseMaterialRequirementDto();
            query.setRequirementIdList(requirementIds);
            List<PurchaseMaterialRequirementDto> requirementList = purchaseMaterialRequirementExtMapper.selectUnreleasedAmountByIds(query);
            requirementMap.putAll(requirementList.stream().collect(Collectors.groupingBy(PurchaseMaterialRequirementDto::getId)));

            //批量查询最新发布日期
            PurchaseMaterialReleaseDetailExample releaseDetailExample = new PurchaseMaterialReleaseDetailExample();
            releaseDetailExample.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andPurchaseRequirementIdIn(requirementIds);
            List<PurchaseMaterialReleaseDetail> releaseDetailList = purchaseMaterialReleaseDetailMapper.selectByExample(releaseDetailExample);
            Map<Long, List<PurchaseMaterialReleaseDetail>> releaseDetailMap = ListUtils.isEmpty(releaseDetailList) ? new HashMap<>()
                    : releaseDetailList.stream().collect(Collectors.groupingBy(PurchaseMaterialReleaseDetail::getPurchaseRequirementId));
            for (Long purchaseRequirementId : releaseDetailMap.keySet()) {
                List<PurchaseMaterialReleaseDetail> detailList = releaseDetailMap.get(purchaseRequirementId);
                PurchaseMaterialReleaseDetail detail =
                        detailList.stream().max(Comparator.comparing(PurchaseMaterialReleaseDetail::getPublishTime)).orElse(null);
                publishTimeMap.put(purchaseRequirementId,
                        Optional.ofNullable(detail).map(PurchaseMaterialReleaseDetail::getPublishTime).orElse(null));
            }
        }

        //批量查询项目
        ProjectExample projectExample = new ProjectExample();
        projectExample.createCriteria().andIdIn(projectIdList);
        Map<Long, Project> projectMap = projectMapper.selectByExample(projectExample).stream().collect(Collectors.toMap(Project::getId,
                Function.identity()));

        //查询当前单位所有业务实体
        List<Long> ouIds = basedataExtService.queryCurrentUnitOu().stream().map(OperatingUnitDto::getOperatingUnitId).collect(Collectors.toList());

        HashMap<String, PurchaseOrderDto> orderMap = new HashMap<>();
        Map<String, List<PurchaseBpaPrice>> purchaseBpaPriceMap = new HashMap();
        Map<String, BigDecimal> conversionRateMap = new HashMap();
        for (PurchaseOrderDetailDto orderDetailDto : orderDetailDtos) {
            Project project = projectMap.get(orderDetailDto.getProjectId());
            if (!ouIds.contains(project.getOuId())) {
                continue;
            }
            orderDetailDto.setOuId(project.getOuId());
            orderDetailDto.setCreateByName(CacheDataUtils.findUserNameById(orderDetailDto.getCreateBy())); //创建人

            Long materialPurchaseRequirementId = orderDetailDto.getMaterialPurchaseRequirementId();
            if (null != materialPurchaseRequirementId) {
                orderDetailDto.setPublishTime(publishTimeMap.get(materialPurchaseRequirementId));
                List<PurchaseMaterialRequirementDto> requirementDtos = requirementMap.get(materialPurchaseRequirementId);
                if (ListUtils.isNotEmpty(requirementDtos)) {
                    orderDetailDto.setUnreleasedAmount(requirementDtos.get(0).getUnreleasedAmount());
                }
            }

            //采购订单分组规则
            String key = null;
            if (purchaseOrderGroupRuleSet.contains("1")) {
                //订单划分维度:项目+供应商
                key = orderDetailDto.getProjectId() + "_" + orderDetailDto.getVendorNum();
            } else if (purchaseOrderGroupRuleSet.contains("2")) {
                //订单划分维度:OU+供应商
                key = orderDetailDto.getOuId() + "_" + orderDetailDto.getVendorNum();
            }

            PurchaseOrderDto orderDto = orderMap.get(key);
            if (orderDto == null) {
                orderDto = new PurchaseOrderDto();
                orderDto.setProjectId(project.getId()); //尚且随机给订单头一个项目id，兼容历史问题
                orderDto.setProjectOuId(project.getOuId());
                orderDto.setProjectOuName(CacheDataUtils.findOuNameById(project.getOuId()));
                orderDto.setOrderStatus(PurchaseOrderStatus.WAIT_ENABLE.code());
                orderDto.setSyncStatus(PurchaseOrderSyncStatus.NOT_SYNCED.code());
                orderDto.setBuyerName(orderDetailDto.getCreateByName());  //采购员
                fillErpBuyerId1(Arrays.asList(orderDto));                 //ERP采购员


                List<PurchaseOrderDetailDto> detailDtos = new ArrayList<>();
                detailDtos.add(orderDetailDto);
                orderDto.setPurchaseOrderDetailDtos(detailDtos);


                if (ListUtils.isNotEmpty(dicts) && "1".equals(dicts.get(0).getValue())) {

                    //根据erp物料编码+供应商编码+供应商地点，查询是否有满足有效期内
                    String erpCode = orderDetailDto.getErpCode();
                    String vendorCode = orderDetailDto.getVendorNum();
                    String vendorSiteCode = orderDetailDto.getVendorSiteCode();

                    String purchaseBpaPriceKey = erpCode + "_" + vendorSiteCode + "_" + vendorCode;
                    List<PurchaseBpaPrice> purchaseBpaPrices = purchaseBpaPriceMap.get(purchaseBpaPriceKey);
                    if (purchaseBpaPrices == null) {
                        PurchaseBpaPriceExample purchaseBpaPriceExample = new PurchaseBpaPriceExample();
                        PurchaseBpaPriceExample.Criteria criteria = purchaseBpaPriceExample.createCriteria();
                        if (vendorCode != null) {
                            criteria.andVendorCodeEqualTo(vendorCode);
                        }
                        Date date = new Date();
                        LocalDate localDate = date.toInstant()
                                .atZone(ZoneId.systemDefault())
                                .toLocalDate();
                        Date dateWithoutTime = Date.from(
                                localDate.atStartOfDay(ZoneId.systemDefault()).toInstant()
                        );

                        criteria.andMaterialCodeEqualTo(erpCode)
                                .andVendorSiteCodeEqualTo(vendorSiteCode)
                                .andDeletedFlagEqualTo(false)
                                .andOuIdEqualTo(project.getOuId())
                                .andEndDateGreaterThanOrEqualTo(dateWithoutTime);
                        purchaseBpaPrices = purchaseBpaPriceMapper.selectByExample(purchaseBpaPriceExample);
                        purchaseBpaPriceMap.put(purchaseBpaPriceKey, purchaseBpaPrices);
                    }
                    if (ListUtils.isNotEmpty(purchaseBpaPrices)) {
                        //当"人民币"兑换"人民币"时，汇率默认给1.00
                        if ("CNY".equals(purchaseBpaPrices.get(0).getCurrency())) {
                            orderDto.setConversionRate(new BigDecimal("1.00"));
                            orderDto.setConversionDate(new Date());
                        } else {
                            String conversionRateKey = "CNY" + purchaseBpaPrices.get(0).getCurrency();
                            BigDecimal conversionRate = conversionRateMap.get(conversionRateKey);
                            if (conversionRate != null) {
                                orderDto.setConversionRate(conversionRate);
                                orderDto.setConversionDate(new Date());

                            } else {
                                //查询当天的汇率覆盖采购订单的汇率信息
                                GlDailyRateQuery queryRate = new GlDailyRateQuery();
                                queryRate.setExchangeDateStart(DateUtil.getBeginningOfDay(new Date()));
                                queryRate.setExchangeDateEnd(DateUtil.getEndingOfDay(new Date()));
                                queryRate.setFromCurrency(purchaseBpaPrices.get(0).getCurrency());
                                queryRate.setToCurrency("CNY");
                                String urlRate = String.format("%sglDailyRate/selectDailyRate", ModelsEnum.BASEDATA.getBaseUrl());
                                String resRate = restTemplate.postForObject(urlRate, queryRate, String.class);
                                if (null != resRate && !"[]".equals(resRate)) {
                                    List<Map<String, Object>> dataList = Utils.jsonStr2List(resRate);
                                    orderDto.setConversionRate(new BigDecimal(dataList.get(0).get("exchangeRate").toString()));
                                    orderDto.setConversionDate(new Date());
                                    conversionRateMap.put(conversionRateKey, orderDto.getConversionRate());
                                }
                            }
                        }
                        //把一揽自协议的币种带出覆盖采购订单的币种
                        orderDto.setCurrency(purchaseBpaPrices.get(0).getCurrency());
                        int size = purchaseBpaPrices.size();
                        AtomicInteger resultNum = new AtomicInteger();
                        purchaseBpaPrices.forEach(p -> {
                            boolean result;
                            //当失效日期为空则无限大，并且分段价格不能为空或者小于等于0
                            if (null == p.getEndDate() && null != p.getPriceOverride() && p.getPriceOverride().compareTo(BigDecimal.ZERO) > 0) {
                                result = true;
                            } else {
                                //分段价格不能为空或者小于等于0
                                if (null == p.getPriceOverride() || p.getPriceOverride().compareTo(BigDecimal.ZERO) <= 0) {
                                    result = false;
                                } else {
                                    //符合开始时间+失效时间区间内
                                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DateUtil.DATE_PATTERN);
                                    String startDateStr = DateUtil.format(p.getStartDate(), DateUtil.DATE_PATTERN);
                                    LocalDate startDateLocal = LocalDate.parse(startDateStr, formatter);
                                    String nowStr = DateUtil.format(new Date(), DateUtil.DATE_PATTERN);
                                    LocalDate nowLocal = LocalDate.parse(nowStr, formatter);
                                    String endDateStr = DateUtil.format(p.getEndDate(), DateUtil.DATE_PATTERN);
                                    logger.info("获取的生效时间:{},失效时间:{},当前时间:{}", startDateStr, endDateStr, nowStr);
                                    LocalDate endDateLocal = LocalDate.parse(endDateStr, formatter);
                                    if ((startDateLocal.isBefore(nowLocal) || startDateLocal.isEqual(nowLocal))
                                            && (endDateLocal.isAfter(nowLocal) || endDateLocal.isEqual(nowLocal))) {
                                        result = true;
                                    } else {
                                        result = false;
                                    }
                                }
                            }
                            if (!result) {
                                resultNum.getAndIncrement();
                            }
                        });
                        if (size == resultNum.intValue()) {
                            orderDto.setPurchaseBpaPrice(false);
                        }
                    } else {
                        orderDto.setPurchaseBpaPrice(false);
                    }
                }

                orderMap.put(key, orderDto);
            } else {
                orderDto.getPurchaseOrderDetailDtos().add(orderDetailDto);
            }
        }
        Collection<PurchaseOrderDto> values = orderMap.values();
        return new ArrayList<>(orderMap.values());
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public PurchaseOrderDto saveWithDetail(PurchaseOrderDto dto, Long userBy) {
        List<PurchaseOrderDetailDto> orderDetailDtos = dto.getPurchaseOrderDetailDtos();
        Asserts.notEmpty(dto, ErrorCode.CTC_ORDER_NOT_NULL);
        Asserts.notEmpty(orderDetailDtos, ErrorCode.CTC_ORDER_DETAILS_NOT_NULL);

        dto.setOrderStatus(PurchaseOrderStatus.APPROVED.code());
        dto.setSyncStatus(PurchaseOrderSyncStatus.NOT_SYNCED.code());
        dto.setNum(generateOrderNum(dto.getProjectOuId()));
        dto.setBuyerId(userBy);
        dto.setBuyerName(CacheDataUtils.findUserNameById(userBy));
        dto.setOuId(dto.getProjectOuId());
        dto.setApprovalTime(new Date());
        PurchaseOrderDto result = this.save(dto, userBy);

        if (ListUtils.isEmpty(orderDetailDtos)) {
            throw new BizException(ErrorCode.CTC_ORDER_DETAILS_NOT_NULL);
        }

        for (PurchaseOrderDetailDto orderDetailDto : orderDetailDtos) {
            if (dto.getChangeDeliveryTime() != null) {
                orderDetailDto.setDeliveryTime(dto.getChangeDeliveryTime());//修改计划交货日期
            }
            orderDetailDto.setPurchaseOrderId(result.getId());
            orderDetailDto.setStatus(PurchaseOrderDetailStatus.ORDER_PLACED.code());
            orderDetailDto.setSyncDeliveryInfoStatus(SyncDeliveryInfoStatusEnum.SYNC_PREPARE.getCode());
        }
        purchaseOrderDetailService.saveBatch(orderDetailDtos, userBy);

        //更新采购需求状态
        List<Long> requirementIdList =
                orderDetailDtos.stream().map(PurchaseOrderDetailDto::getMaterialPurchaseRequirementId).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(requirementIdList)) {
            purchaseMaterialRequirementService.batchUpdateStatus(requirementIdList);
        }

        if (result != null) {
            Long ouId = result.getOuId();
            String vendorNum = result.getVendorNum();
            Boolean vendorMatchingConfigAndPushToFap = isVendorMatchingConfigAndPushToFap(ouId, vendorNum);
            if (vendorMatchingConfigAndPushToFap) {
                HandleDispatcher.route(BusinessTypeEnums.PURCHASE_ORDER_FAP.getCode(), String.valueOf(result.getId()), null, null, true, null,
                        dto.getProjectOuId());
                updateSyncToFapStatus(result.getId());
            } else {
                HandleDispatcher.route(BusinessTypeEnums.PURCHASE_ORDER.getCode(), String.valueOf(result.getId()), null, null, true, null,
                        dto.getProjectOuId());
            }
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<PurchaseOrderDto> saveWithDetailBatch(List<PurchaseOrderDto> dtos, Long userBy) {
        List<PurchaseOrderDto> purchaseOrderDtoList = new ArrayList<>();
        String lockName = String.format("purchaseOrder_saveWithDetailBatch_%s", userBy);
        String currentDateStr = DateUtil.format(new Date(), "yyyyMMdd");
        try {
            if (DistributedCASLock.lock(lockName, currentDateStr, MAX_WAIT_TIME, MAX_WAIT_TIME * 2)) {
                checkIfChange(dtos);
                fillErpBuyerId1(dtos);
                for (PurchaseOrderDto dto : dtos) {
                    PurchaseOrderDto purchaseOrderDto = this.saveWithDetail(dto, userBy);
                    purchaseOrderDtoList.add(purchaseOrderDto);
                }
            }
        } catch (Exception e) {
            logger.error("采购订单保存出现异常", e);
            throw e;
        } finally {
            DistributedCASLock.unLock(lockName, currentDateStr);
        }
        return purchaseOrderDtoList;
    }

    /**
     * 数据校验
     *
     * @param dtos
     */
    private void checkIfChange(List<PurchaseOrderDto> dtos) {
        List<PurchaseOrderDetailDto> list = new ArrayList<>();
        dtos.forEach(s -> list.addAll(s.getPurchaseOrderDetailDtos()));
        List<Long> detailNewIds = list.stream().map(PurchaseOrderDetailDto::getId).collect(Collectors.toList());

        PurchaseOrderDetailExample checkExample = new PurchaseOrderDetailExample();
        checkExample.createCriteria().andIdIn(detailNewIds).andDeletedFlagEqualTo(DeletedFlag.VALID.code());
        List<PurchaseOrderDetail> detailNewList = purchaseOrderDetailMapper.selectByExample(checkExample);
        //校验提交的数据是否已经被移除
        if (detailNewList.size() < list.size()) {
            throw new MipException("数据有变动，请刷新");
        }
        //校验是否重复提交
        if (detailNewList.stream().anyMatch(s -> s.getPurchaseOrderId() != null)) {
            throw new MipException("订单已下达，请勿重复操作");
        }
    }

    private void fillErpBuyerId1(List<PurchaseOrderDto> dtos) {
        for (PurchaseOrderDto dto : dtos) {
            List<SdpBuyersDto> buyers = buyersService.getBuyer(dto.getProjectOuId());
            if (buyers.isEmpty()) {
                throw new BizException(Code.ERROR, "未维护有效的ERP采购员");
            } else if (buyers.size() > 1) {
                throw new BizException(Code.ERROR, "员工编码存在多条记录");
            }
            dto.setErpBuyerId(buyers.get(0).getErpAgentId());
            dto.setErpBuyerName(buyers.get(0).getAgentName());
        }
    }

    @Override
    public PurchaseOrderDto getDetailById(Long id) {
        Asserts.notEmpty(id, ErrorCode.ID_NOT_NULL);

        PurchaseOrderDto dto = this.getById(id);
        Asserts.notEmpty(dto.getVendorAslId(), ErrorCode.CTC_ORDER_VENDOR_ID_NOT_NULL);

        PurchaseOrderDetailDto orderDetailQuery = new PurchaseOrderDetailDto();
        orderDetailQuery.setPurchaseOrderId(id);
        List<PurchaseOrderDetailDto> orderDetailList = purchaseOrderDetailService.selectList(orderDetailQuery);
        dto.setPurchaseOrderDetailDtos(orderDetailList);

        dto.setErpBuyerName(getErpBuyerName(dto.getErpBuyerId()));

        //查询变更记录
        List<PurchaseOrderChangeRecordDto> recordList = new ArrayList<>();
        PurchaseOrderChangeHistoryExample example = new PurchaseOrderChangeHistoryExample();
        example.createCriteria().andOriginIdEqualTo(id).andDeletedFlagEqualTo(DeletedFlag.VALID.code());
        List<PurchaseOrderChangeHistory> orderChangeHistoryList = purchaseOrderChangeHistoryMapper.selectByExample(example);
        //1.待变更的记录
        PurchaseOrderChangeHistory orderChangeHistory = orderChangeHistoryList.stream().filter(s -> Objects.equals(s.getOrderStatus(),
                PurchaseOrderStatus.CHANGING.code())).findFirst().orElse(null);
        if (orderChangeHistory != null) {
            PurchaseOrderChangeRecordDto changingRecord = new PurchaseOrderChangeRecordDto();
            changingRecord.setChangeType("新增订单行");
            changingRecord.setStatus(PurchaseOrderChangeRecordStatus.CHANGING.code());
            changingRecord.setCreateBy(orderChangeHistory.getCreateBy());
            recordList.add(changingRecord);
        }
        //2.已提交变更的记录
        List<Long> recordIdList = orderChangeHistoryList.stream().map(PurchaseOrderChangeHistory::getRecordId).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(recordIdList)) {
            PurchaseOrderChangeRecordExample recordExample = new PurchaseOrderChangeRecordExample();
            recordExample.setOrderByClause("create_at desc");
            recordExample.createCriteria().andIdIn(recordIdList).andDeletedFlagEqualTo(DeletedFlag.VALID.code());
            recordList.addAll(BeanConverter.copy(purchaseOrderChangeRecordMapper.selectByExample(recordExample), PurchaseOrderChangeRecordDto.class));
        }
        recordList.forEach(s -> s.setCreateByName(CacheDataUtils.findUserNameById(s.getCreateBy())));
        dto.setPurchaseOrderChangeRecordDtos(recordList);
        return dto;
    }

    @Override
    public String getErpBuyerName(Long erpBuyerId) {
        if (erpBuyerId != null) {
            SdpBuyersExample example = new SdpBuyersExample();
            example.createCriteria()
                    .andErpAgentIdEqualTo(erpBuyerId)
                    .andDeleteFlagEqualTo(Boolean.FALSE);
            example.setOrderByClause("create_at desc");
            List<SdpBuyers> sdpBuyers = buyersMapper.selectByExample(example);
            if (!sdpBuyers.isEmpty()) {
                return sdpBuyers.get(0).getAgentName();
            }
        }
        return null;
    }

    /**
     * 构建 PAM-ERP-034 ERP传输对象
     *
     * @param purchaseOrderId 采购订单头id
     * @param changeRecordId  变更记录id
     * @return
     */
    @Override
    public PurchaseOrderDto buildPushOrderDto(Long purchaseOrderId, Long changeRecordId) {
        Asserts.notEmpty(purchaseOrderId, ErrorCode.ID_NOT_NULL);
        //订单头信息
        PurchaseOrderDto dto = this.getById(purchaseOrderId);

        //供应商名称查供应商主表，保证正确性和实时性
        List<VendorSiteBankDto> vendorSiteBankList = basedataExtService.getVendorSiteBankByCodes(Arrays.asList(dto.getVendorNum()));
        vendorSiteBankList.removeIf(vendor -> !Objects.equals(vendor.getOperatingUnitId(), dto.getOuId()));
        vendorSiteBankList.removeIf(vendor -> !Objects.equals(vendor.getPurchasingSiteFlag(), "Y"));
        if (CollectionUtils.isNotEmpty(vendorSiteBankList)) {
            dto.setVendorName(vendorSiteBankList.get(0).getVendorName());
            dto.setErpVendorSiteId(vendorSiteBankList.get(0).getErpVendorSiteId());
            dto.setVendorId(vendorSiteBankList.get(0).getErpVendorId());
            dto.setVendorSiteCode(vendorSiteBankList.get(0).getVendorSiteCode());
        }

        if (dto.getOuId() != null) {
            //按照采购订单所属OU查询采购订单类型.  组织参数配置值：格式：XX:XXX，例如：CB01:订单类型1.  仅获取“：”前面的数据，例如：CB01
            Set<String> valueSet = organizationCustomDictService.queryByName("采购订单类型", dto.getOuId(), OrgCustomDictOrgFrom.OU);
            if (!CollectionUtils.isEmpty(valueSet) && valueSet.size() == 1) {
                String next = valueSet.iterator().next();
                if (StringUtils.isNotEmpty(next) || next.split(":").length == 2) {
                    dto.setOrderType(next.split(":")[0]);
                }
            }
        }

        //订单行信息
        PurchaseOrderDetailDto orderDetailQuery = new PurchaseOrderDetailDto();
        orderDetailQuery.setPurchaseOrderId(purchaseOrderId);
        if (!Objects.isNull(changeRecordId)) {
            orderDetailQuery.setRecordId(changeRecordId);  // 根据变更记录推送增量数据
        } else {
            //没有变更记录id，则推送所有未同步的收货信息
            orderDetailQuery.setSyncDeliveryInfoStatus(SyncDeliveryInfoStatusEnum.SYNC_PREPARE.getCode());
        }

        List<PurchaseOrderDetailDto> purchaseOrderDetailDtoList = purchaseOrderDetailService.selectList(orderDetailQuery);
        packagePushDto(purchaseOrderDetailDtoList, dto);
        dto.setPurchaseOrderDetailDtos(purchaseOrderDetailDtoList);

        return dto;
    }

    private void packagePushDto(List<PurchaseOrderDetailDto> detailDtos, PurchaseOrderDto dto) {
        if (CollectionUtils.isEmpty(detailDtos)) return;

        //批量查询收入成本计划
        ProjectProfitExample projectProfitExample = new ProjectProfitExample();
        List<Long> projectIdList = detailDtos.stream().map(PurchaseOrderDetailDto::getProjectId).collect(Collectors.toList());
        projectProfitExample.createCriteria().andProjectIdIn(projectIdList).andDeletedFlagEqualTo(DeletedFlag.VALID.code());
        Map<Long, ProjectProfit> projectProfitMap = projectProfitMapper.selectByExample(projectProfitExample).stream()
                .collect(Collectors.toMap(ProjectProfit::getProjectId, Function.identity()));

        //批量查询最新发布日期
        List<Long> requirementIdList = detailDtos.stream().map(PurchaseOrderDetailDto::getMaterialPurchaseRequirementId).collect(Collectors.toList());
        Map<Long, Date> publishTimeMap = purchaseMaterialRequirementExtMapper.selectLatestPublishTime(requirementIdList).stream()
                .collect(Collectors.toMap(PurchaseMaterialReleaseDetailDto::getPurchaseRequirementId,
                        PurchaseMaterialReleaseDetailDto::getPublishTime));

        for (PurchaseOrderDetailDto detailDto : detailDtos) {
            ProjectProfit projectProfit = projectProfitMap.get(detailDto.getProjectId());
            if (projectProfit != null && projectProfit.getStorageId() != null) {
                //库存组织
                detailDto.setOrganizationId(projectProfit.getStorageId());
                //子库存代码


                //采购订单号 无论WBS还是非WBS的采购订单，接收子库都取采购订单头存的接收子库
                detailDto.setSecondaryInventoryName(dto.getSecondaryInventoryName());
//                StorageInventory storageInventory = storageInventoryExtService.findByOrganizationIdAndTypeList(projectProfit.getStorageId(),
//                        Lists.newArrayList(StorageInventoryType.RECEIVING_WAREHOUSE.code(), StorageInventoryType.FEE_WAREHOUSE.code()));
                //detailDto.setSecondaryInventoryName(Optional.ofNullable(storageInventory).map(StorageInventory::getSecondaryInventoryName).orElse
                // (null));
            }
            //最新发布日期
            Date publishTime = publishTimeMap.get(detailDto.getMaterialPurchaseRequirementId());
            if (publishTime != null) {
                detailDto.setPublishTime(publishTime);
            }
        }
    }

    @Override
    public PurchaseOrderDto getDetailWbsById(Long id) {
        Asserts.notEmpty(id, ErrorCode.ID_NOT_NULL);

        PurchaseOrderDto dto = this.getById(id);
        Asserts.notEmpty(dto.getVendorAslId(), ErrorCode.CTC_ORDER_VENDOR_ID_NOT_NULL);

        if (dto.getProjectId() != null) {
            dto.setProjectDto(projectService.findDetail(dto.getProjectId()));
        }
        dto.setVendorAsl(vendorAslService.getById(dto.getVendorAslId()));
        if (dto.getVendorAsl() != null) {
            dto.setVendorNum(dto.getVendorAsl().getVendorCode());
        }
        if (dto.getProjectDto() != null) {
            dto.setProjectNum(dto.getProjectDto().getCode());
        }
        // 订单状态为草稿,且定价类型是一揽子协议,需要更新一揽子价格
        boolean condition = isUpdatePrice(dto.getOrderStatus(), dto.getPricingType());
        if (condition) {
            updateOrderPrice(dto);
        }
        PurchaseOrderDetailDto orderDetailQuery = new PurchaseOrderDetailDto();
        orderDetailQuery.setPurchaseOrderId(id);
        List<PurchaseOrderDetailDto> purchaseOrderDetailDtoList = purchaseOrderDetailService.selectList(orderDetailQuery);

        //批量查询 需求预算占用金额(汇总)
        List<Long> projectWbsReceiptsIdList = purchaseOrderDetailDtoList.stream().map(PurchaseOrderDetailDto::getProjectWbsReceiptsId).filter(Objects::nonNull).collect(Collectors.toList());
        projectWbsReceiptsIdList.add(-1L); //防空
        ProjectWbsReceiptsBudgetExample receiptsBudgetExample = new ProjectWbsReceiptsBudgetExample();
        receiptsBudgetExample.createCriteria().andProjectWbsReceiptsIdIn(projectWbsReceiptsIdList)
                .andDemandTypeEqualTo(ProjectWbsReceiptsBudgetDemandTypeEnums.ALL.getCode())
                .andDeletedFlagEqualTo(Boolean.FALSE);
        List<ProjectWbsReceiptsBudget> projectWbsReceiptsBudgetList = projectWbsReceiptsBudgetMapper.selectByExample(receiptsBudgetExample);
        Map<String, BigDecimal> budgetOccupiedAmountTotalMap = projectWbsReceiptsBudgetList.stream().collect(Collectors.toMap(s -> buildProjectWbsReceiptsBudgetKey(s.getWbsSummaryCode(), s.getProjectWbsReceiptsId()), s -> s.getBudgetOccupiedAmount(), (s1, s2) -> s1));

        for (int i = 0; i < purchaseOrderDetailDtoList.size(); i++) {
            PurchaseOrderDetailDto purchaseOrderDetailDto = purchaseOrderDetailDtoList.get(i);
            purchaseOrderDetailDto.setRequirementId(purchaseOrderDetailDto.getMaterialPurchaseRequirementId());
            //此处明细内容展示的折后金额(不含税)需要扣减掉取消数量，计算公式： 折后金额(不含税) = 折后价(不含税) ✖ 实际下单数量
            BigDecimal actualNum = Optional.ofNullable(purchaseOrderDetailDto.getOrderNum()).orElse(BigDecimal.ZERO)
                    .subtract(Optional.ofNullable(purchaseOrderDetailDto.getCancelNum()).orElse(BigDecimal.ZERO));
            BigDecimal discountMoney = Optional.ofNullable(purchaseOrderDetailDto.getDiscountPrice()).orElse(BigDecimal.ZERO)
                    .multiply(actualNum).setScale(2, RoundingMode.HALF_UP);
            purchaseOrderDetailDto.setDiscountMoney(discountMoney);

            //需求预算占用金额(汇总)
            String key = buildProjectWbsReceiptsBudgetKey(purchaseOrderDetailDto.getWbsSummaryCode(), purchaseOrderDetailDto.getProjectWbsReceiptsId());
            purchaseOrderDetailDto.setBudgetOccupiedAmountTotal(budgetOccupiedAmountTotalMap.getOrDefault(key, BigDecimal.ZERO));

            PurchaseOrderMergeExample purchaseOrderMergeExample = new PurchaseOrderMergeExample();
            purchaseOrderMergeExample.createCriteria()
                    .andPurchaseOrderIdEqualTo(purchaseOrderDetailDto.getId())
                    .andMergeRowsEqualTo(1);
            List<PurchaseOrderMerge> purchaseOrderMergeList = purchaseOrderMergeMapper.selectByExample(purchaseOrderMergeExample);
            List<PurchaseOrderDetailDto> purchaseOrderDetailDtoArrayList = new ArrayList<>();
            for (int j = 0; j < purchaseOrderMergeList.size(); j++) {
                PurchaseOrderDetailDto mergeOrderDetailDto = new PurchaseOrderDetailDto();
                BeanUtils.copyProperties(purchaseOrderMergeList.get(j), mergeOrderDetailDto);
                // purchase_order_merge 没有 projectWbsReceiptsId
                mergeOrderDetailDto.setProjectWbsReceiptsId(purchaseOrderDetailDto.getProjectWbsReceiptsId());
                mergeOrderDetailDto.setRequirementId(mergeOrderDetailDto.getMaterialPurchaseRequirementId());
                //此处明细内容展示的折后金额(不含税)需要扣减掉取消数量，计算公式： 折后金额(不含税) = 折后价(不含税) ✖ 实际下单数量
                BigDecimal actualNumMerge = Optional.ofNullable(mergeOrderDetailDto.getOrderNum()).orElse(BigDecimal.ZERO)
                        .subtract(Optional.ofNullable(mergeOrderDetailDto.getCancelNum()).orElse(BigDecimal.ZERO));
                BigDecimal discountMoneyMerge = Optional.ofNullable(mergeOrderDetailDto.getDiscountPrice()).orElse(BigDecimal.ZERO)
                        .multiply(actualNumMerge).setScale(2, RoundingMode.HALF_UP);
                mergeOrderDetailDto.setDiscountMoney(discountMoneyMerge);
                purchaseOrderDetailDtoArrayList.add(mergeOrderDetailDto);
            }
            purchaseOrderDetailDtoList.get(i).setChildren(purchaseOrderDetailDtoArrayList);
        }
        // 根据每个采购订单行/合并行的
        setPurchaseOrderDetailRemainMoney(purchaseOrderDetailDtoList);
        dto.setPurchaseOrderDetailDtos(purchaseOrderDetailDtoList);

        // get文件数据  start
        CtcAttachmentExample ctcAttachmentExample = new CtcAttachmentExample();
        CtcAttachmentExample.Criteria criteria1 = ctcAttachmentExample.createCriteria();
        criteria1.andModuleEqualTo(CtcAttachmentModule.PURCHASE_ORDER.code());
        criteria1.andModuleIdEqualTo(id);
        criteria1.andDeletedFlagEqualTo(DeletedFlag.VALID.code());
        List<CtcAttachment> ctcAttachmentList = ctcAttachmentMapper.selectByExample(ctcAttachmentExample);
        List<CtcAttachmentDto> ctcAttachmentDtoList = new ArrayList<>();
        for (int i = 0; i < ctcAttachmentList.size(); i++) {
            CtcAttachmentDto ctcAttachmentDto = new CtcAttachmentDto();
            BeanUtils.copyProperties(ctcAttachmentList.get(i), ctcAttachmentDto);
            UserInfo user = CacheDataUtils.findUserById(ctcAttachmentList.get(i).getCreateBy());
            if (user != null) {
                ctcAttachmentDto.setCreateUserName(user.getName());
            }
            ctcAttachmentDtoList.add(ctcAttachmentDto);
        }
        dto.setCtcAttachmentList(ctcAttachmentDtoList);
        // get文件数据  end

        PurchaseOrder purchaseOrder = purchaseOrderMapper.selectByPrimaryKey(id);
        PurchaseOrderTitleDto purchaseOrderTitle = new PurchaseOrderTitleDto();
        BeanUtils.copyProperties(purchaseOrder, purchaseOrderTitle);
        dto.setPurchaseOrderTitle(purchaseOrderTitle);

        //查询变更记录 start
        PurchaseOrderChangeHistoryExample purchaseOrderChangeHistoryExample = new PurchaseOrderChangeHistoryExample();
        purchaseOrderChangeHistoryExample.createCriteria().andOriginIdEqualTo(id);
        List<PurchaseOrderChangeHistory> purchaseOrderChangeHistoryList =
                purchaseOrderChangeHistoryMapper.selectByExample(purchaseOrderChangeHistoryExample);
        if (purchaseOrderChangeHistoryList != null && purchaseOrderChangeHistoryList.size() > 0) {
            List<Long> values = new ArrayList<>();
            for (PurchaseOrderChangeHistory purchaseOrderChangeHistory : purchaseOrderChangeHistoryList) {
                values.add(purchaseOrderChangeHistory.getRecordId());
            }
            PurchaseOrderChangeRecordExample purchaseOrderChangeRecordExample = new PurchaseOrderChangeRecordExample();
            purchaseOrderChangeRecordExample.createCriteria().andIdIn(values);
            List<PurchaseOrderChangeRecord> purchaseOrderChangeRecordList =
                    purchaseOrderChangeRecordMapper.selectByExample(purchaseOrderChangeRecordExample);
            dto.setPurchaseOrderChangeRecordList(purchaseOrderChangeRecordList);
        }
        dto.setErpBuyerName(getErpBuyerName(dto.getErpBuyerId()));
        purchaseOrderTitle.setErpBuyerName(dto.getErpBuyerName());
        purchaseOrderTitle.setCurrencyCode(dto.getCurrency());
        //查询变更记录 end

        //查询标准条款信息
        standardTermsSet(purchaseOrderTitle,dto);
        return dto;
    }

    private void standardTermsSet(PurchaseOrderTitleDto purchaseOrderTitle,PurchaseOrderDto dto) {
        if (ContractTermsFlgEnum.STANDARD_TERMS.getCode().equals(dto.getContractTermsFlg())) {
            PurchaseOrderStandardTermsExample purchaseOrderStandardTermsExample = new PurchaseOrderStandardTermsExample();
            PurchaseOrderStandardTermsExample.Criteria termsExampleCriteria = purchaseOrderStandardTermsExample.createCriteria();
            termsExampleCriteria.andDeletedFlagEqualTo(false).andPurchaseOrderIdEqualTo(dto.getId());
            List<PurchaseOrderStandardTerms> purchaseOrderStandardTerms = purchaseOrderStandardTermsMapper.selectByExampleWithBLOBs(purchaseOrderStandardTermsExample);
            if (CollectionUtils.isNotEmpty(purchaseOrderStandardTerms)) {
                List<PurchaseOrderStandardTermsDto> purchaseOrderStandardTermsDtoList = new ArrayList<>();
                purchaseOrderStandardTermsDtoListSave(purchaseOrderStandardTerms,purchaseOrderStandardTermsDtoList);
                List<StandardTermsDto> standardTermsDtoList = new ArrayList<>();
                for (PurchaseOrderStandardTermsDto purchaseOrderStandardTermsDto : purchaseOrderStandardTermsDtoList) {
                    StandardTermsDto standardTermsDto = new StandardTermsDto();
                    standardTermsDto.setId(purchaseOrderStandardTermsDto.getId());
                    standardTermsDto.setPurchaseOrderId(purchaseOrderStandardTermsDto.getPurchaseOrderId());
                    standardTermsDto.setAssociationTermsId(purchaseOrderStandardTermsDto.getAssociationTermsId());
                    standardTermsDto.setTermsCode(purchaseOrderStandardTermsDto.getTermsCode());
                    standardTermsDto.setTermsName(purchaseOrderStandardTermsDto.getTermsName());
                    standardTermsDto.setRemark(purchaseOrderStandardTermsDto.getRemark());
                    standardTermsDto.setTermsDisplayContent(purchaseOrderStandardTermsDto.getTermsDisplayContent());
                    standardTermsDto.setStandardTermsDeviationList(purchaseOrderStandardTermsDto.getStandardTermsDeviationList());
                    standardTermsDtoList.add(standardTermsDto);
                }
                purchaseOrderTitle.setStandardTermsDtoList(standardTermsDtoList);
            }
        }
    }

    /**
     * 统计需求发布的剩余需求预算
     *
     * @param purchaseOrderDetailList
     */
    @Override
    public void setPurchaseOrderDetailRemainMoney(List<PurchaseOrderDetailDto> purchaseOrderDetailList) {
        if (ListUtils.isEmpty(purchaseOrderDetailList)) {
            return;
        }
        // 根据需求发布单据+wbscode查询已占用预算(折后不含税-取消数量*单价)
        List<Long> projectWbsReceiptsIdList = getProjectWbsReceiptsIdList(purchaseOrderDetailList);
        if (ListUtils.isEmpty(projectWbsReceiptsIdList)) {
            return;
        }

        //采购订单占用汇总
        List<PurchaseOrderDetailDto> discountMoneyList = purchaseOrderExtMapper.getDiscountMoneyByProjectWbsReceiptsIdList(projectWbsReceiptsIdList, null, null);
        Map<String, List<PurchaseOrderDetailDto>> progressNumMap = discountMoneyList.stream().collect(Collectors.groupingBy(orderDetail -> orderDetail.getWbsSummaryCode() + ":" + orderDetail.getProjectWbsReceiptsId()));

        //采购合同占用汇总
        Map<String, Object> param = new HashMap<>();
        param.put("projectWbsReceiptsIdList", projectWbsReceiptsIdList);
        param.put("purchaseType", PurchaseMaterialRequirementPurchaseTypeEnums.OUTSOURCE.getCode());
        List<PurchaseContractBudgetDto> wbsContractBudgetList = purchaseContractBudgetExtMapper.getWbsContractBudgetByParam(param);
        Map<String, List<PurchaseContractBudgetDto>> contractDetailMap = wbsContractBudgetList.stream().collect(Collectors.groupingBy(s -> s.getWbsSummaryCode() + ":" + s.getReceiptsId()));

        for (PurchaseOrderDetailDto orderDetail : purchaseOrderDetailList) {
            // 是否合并行
            if (ListUtils.isNotEmpty(orderDetail.getChildren())) {
                for (PurchaseOrderDetailDto child : orderDetail.getChildren()) {
                    calculateRemainMoney(child, progressNumMap, contractDetailMap);
                }
            } else {
                calculateRemainMoney(orderDetail, progressNumMap, contractDetailMap);
            }
        }
    }

    @Override
    public void calculateRemainMoney(PurchaseOrderDetailDto orderDetail, Map<String, List<PurchaseOrderDetailDto>> progressNumMap) {
        BigDecimal budgetOccupiedAmount = orderDetail.getBudgetOccupiedAmount() != null ? orderDetail.getBudgetOccupiedAmount() : BigDecimal.ZERO;
        orderDetail.setRemainMoney(budgetOccupiedAmount);
        if (Objects.isNull(orderDetail.getProjectWbsReceiptsId()) || StringUtils.isEmpty(orderDetail.getRequirementCode())
                || StringUtils.isEmpty(orderDetail.getWbsSummaryCode())) {
            return;
        }
        // 根据需求发布单据+wbscode查询已占用预算(折后不含税-取消数量*单价)
        List<PurchaseOrderDetailDto> discountMoneyList =
                progressNumMap.get(orderDetail.getWbsSummaryCode() + ":" + orderDetail.getProjectWbsReceiptsId());
        if (ListUtils.isEmpty(discountMoneyList)) {
            return;
        }
        BigDecimal totalDiscountMoney = discountMoneyList.stream().map(e -> Optional.ofNullable(e.getDiscountMoney()).orElse(BigDecimal.ZERO))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        orderDetail.setRemainMoney(budgetOccupiedAmount.subtract(totalDiscountMoney));
    }

    public void calculateRemainMoney(PurchaseOrderDetailDto orderDetail, Map<String, List<PurchaseOrderDetailDto>> progressNumMap, Map<String, List<PurchaseContractBudgetDto>> contractDetailMap) {
        //预算占用金额（汇总）
        BigDecimal budgetOccupiedAmountTotal = Optional.ofNullable(orderDetail.getBudgetOccupiedAmountTotal()).orElse(BigDecimal.ZERO);
        orderDetail.setRemainMoney(budgetOccupiedAmountTotal);
        if (Objects.isNull(orderDetail.getProjectWbsReceiptsId()) || StringUtils.isEmpty(orderDetail.getRequirementCode())
                || StringUtils.isEmpty(orderDetail.getWbsSummaryCode())) {
            return;
        }

        //累计采购订单占用金额
        List<PurchaseOrderDetailDto> orderDetailList = progressNumMap.get(orderDetail.getWbsSummaryCode() + ":" + orderDetail.getProjectWbsReceiptsId());
        BigDecimal totalDiscountMoney = CollectionUtils.isEmpty(orderDetailList) ? BigDecimal.ZERO :
                orderDetailList.stream().map(PurchaseOrderDetailDto::getDiscountMoney).reduce(BigDecimal.ZERO, BigDecimalUtils::add);

        //累计采购合同占用金额
        BigDecimal contractTotalAmount = BigDecimal.ZERO;
        List<PurchaseContractBudgetDto> contractDetailList = contractDetailMap.get(orderDetail.getWbsSummaryCode() + ":" + orderDetail.getProjectWbsReceiptsId());
        if (CollectionUtils.isNotEmpty(contractDetailList) && contractDetailList.get(0).getContractTotalAmount() != null) {
            contractTotalAmount = contractDetailList.get(0).getContractTotalAmount();
        }

        //剩余可用需求预算 = 预算占用金额（汇总） - 累计采购合同占用金额 - 累计采购订单占用金额
        orderDetail.setRemainMoney(budgetOccupiedAmountTotal.subtract(contractTotalAmount).subtract(totalDiscountMoney));
    }

    private List<Long> getProjectWbsReceiptsIdList(List<PurchaseOrderDetailDto> purchaseOrderDetailList) {
        List<Long> projectWbsReceiptsIdList = new ArrayList<>();
        for (PurchaseOrderDetailDto orderDetail : purchaseOrderDetailList) {
            // 是否合并行
            if (ListUtils.isNotEmpty(orderDetail.getChildren())) {
                for (PurchaseOrderDetailDto child : orderDetail.getChildren()) {
                    projectWbsReceiptsIdList.add(child.getProjectWbsReceiptsId());
                }
            } else {
                projectWbsReceiptsIdList.add(orderDetail.getProjectWbsReceiptsId());
            }
        }
        return projectWbsReceiptsIdList;
    }

    @Override
    public Long getRequirementIdByFilter(String itemCode, Long projectId, Date deliveryTime) {
        Asserts.notEmpty(itemCode, ErrorCode.CTC_ITEM_CODE_NOT_NULL);
        Asserts.notEmpty(projectId, ErrorCode.CTC_PROJECT_ID_NULL);
        Asserts.notEmpty(deliveryTime, ErrorCode.CTC_MILEPOST_DESIGN_PLAN_DELIVERY_TIME_NULL);

        PurchaseMaterialRequirementDto requirementQuery = new PurchaseMaterialRequirementDto();
        requirementQuery.setProjectId(projectId);
        requirementQuery.setErpCode(itemCode);
        requirementQuery.setDeliveryTime(deliveryTime);

        List<Long> requirementIdList = purchaseMaterialRequirementExtMapper.selectIdsWithDetail(requirementQuery);
        if (ListUtil.isPresent(requirementIdList)) {
            return requirementIdList.get(0);
        }
        return null;
    }

    @Override
    public BigDecimal getOrderNumSum(String itemCode, Long projectId, Date deliveryTime) {
        Asserts.notEmpty(itemCode, ErrorCode.CTC_ITEM_CODE_NOT_NULL);
        Asserts.notEmpty(projectId, ErrorCode.CTC_PROJECT_ID_NULL);
        Asserts.notEmpty(deliveryTime, ErrorCode.CTC_MILEPOST_DESIGN_PLAN_DELIVERY_TIME_NULL);

        BigDecimal orderNumSum = new BigDecimal(0);

        PurchaseMaterialRequirementDto requirementQuery = new PurchaseMaterialRequirementDto();
        requirementQuery.setProjectId(projectId);
        requirementQuery.setErpCode(itemCode);
        requirementQuery.setDeliveryTime(deliveryTime);

        List<Long> requirementIdList = purchaseMaterialRequirementExtMapper.selectIdsWithDetail(requirementQuery);
        if (ListUtil.isPresent(requirementIdList)) {
            List<PurchaseMaterialRequirementDto> requirementDtos =
                    purchaseMaterialRequirementExtMapper.selectReleasedQuantityByIds(requirementIdList);
            if (ListUtil.isPresent(requirementDtos)) {
                if (requirementDtos.get(0).getReleasedQuantity() != null) {
                    orderNumSum = requirementDtos.get(0).getReleasedQuantity();
                }
            }
        }

//        List<PurchaseMaterialRequirementDto> requirementDtos = purchaseMaterialRequirementExtMapper.selectListWithDetail(requirementQuery);
//        if (ListUtil.isPresent(requirementDtos)) {
//            if (requirementDtos.get(0).getReleasedQuantity() != null) {
//                orderNumSum = requirementDtos.get(0).getReleasedQuantity();
//            }
//        }
        return orderNumSum;
    }

    @Override
    public void asynStatusFromErp(final String date) {
        String startDateTime = date;
        String endDateTime = null;
        if (StringUtils.isEmpty(startDateTime)) {
            endDateTime = DateUtils.format(new Date(), DateUtils.FORMAT_LONG);//当前时间
            startDateTime = com.midea.mcomponent.core.util.date.DateUtil.addDay(endDateTime, -2);//当前时间-2天
        } else {
            //指定的开始时间+1小时
            endDateTime = com.midea.mcomponent.core.util.date.DateUtil.addDay(startDateTime, 1);
        }
        final List<OrganizationRel> rels = getValidOrganization();
        if (ListUtils.isEmpty(rels)) return;
        for (OrganizationRel rel : rels) {
            if (Objects.equals(rel.getOrganizationId(), rel.getOperatingUnitId())) {
                continue;
            }
            final Map<String, String> params = new HashMap();
//            params.put(EsbConstant.ERP_IP_P01, String.valueOf(rel.getOperatingUnitId()));
//            params.put(EsbConstant.ERP_IP_P03, startDateTime);
//            params.put(EsbConstant.ERP_IP_P04, endDateTime);
            params.put(EsbConstant.ERP_SDP_P01, String.valueOf(rel.getOperatingUnitId()));
            params.put(EsbConstant.ERP_SDP_P03, startDateTime);
            params.put(EsbConstant.ERP_SDP_P04, endDateTime);
//            List<ERPMassQueryReturnVo> returnItemList = esbService.callEsbMassQuery(EsbConstant.ERP_PIFACECODE_044, params);
            List<SdpTradeResultResponseEleDto> returnItemList = sdpCarrierServicel.callSdpMassQuery(EsbConstant.ERP_PIFACECODE_044, params);
            if (ListUtils.isEmpty(returnItemList)) {
                continue;
            }
            for (SdpTradeResultResponseEleDto returnVo : returnItemList) {
                //处理时间没同步更新问题，MR2023061594512-梁庆坤-子需求1：PAM功能优化-相关日期的逻辑
                //String podType = returnVo.getC18(); //发运行处理类型
                //自动同步只处理ERP接口pod_type字段为空的数据
//                if (StringUtil.isNotNull(podType)) {
//                    continue;
//                }
                //头数据
                PurchaseOrderExample orderExample = new PurchaseOrderExample();
                orderExample.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andNumEqualTo(returnVo.getC3());
                List<PurchaseOrder> orderList = purchaseOrderMapper.selectByExample(orderExample);
                if (CollectionUtils.isEmpty(orderList)) {
                    continue;
                }
                PurchaseOrder purchaseOrder = orderList.get(0);
                if (!StringUtils.isEmpty(returnVo.getC30())) {
                    //发布状态
                    purchaseOrder.setErpOrderStatus(ErpOrderStatus.getCode(returnVo.getC30()));
                }
                //头取消状态
                if ("Y".equals(returnVo.getC6())) {
                    purchaseOrder.setOrderStatus(PurchaseOrderStatus.CANCELED.code());
                } else if ("Y".equals(returnVo.getC7())) {
                    //冻结状态
                    purchaseOrder.setOrderStatus(PurchaseOrderStatus.FROZEN.code());
                } else if ("Y".equals(returnVo.getC8())) {
                    //暂挂状态
                    purchaseOrder.setOrderStatus(PurchaseOrderStatus.ONHOLD.code());
                } else if (!"OPEN".equals(returnVo.getC5())) {
                    //不是打开状态
                    purchaseOrder.setOrderStatus(PurchaseOrderStatus.CLOSED.code());
                }
                Integer lineNum = Integer.parseInt(returnVo.getC10());

                //行数据
                PurchaseOrderDetailExample example = new PurchaseOrderDetailExample();
                example.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE)
                        .andPurchaseOrderIdEqualTo(purchaseOrder.getId()).andLineNumberEqualTo(lineNum);
                List<PurchaseOrderDetail> orderDetails = purchaseOrderDetailMapper.selectByExample(example);
                if (CollectionUtils.isEmpty(orderDetails)) {
                    continue;
                }
                PurchaseOrderDetail orderDetail = orderDetails.get(0);
                try {
                    //供方承诺交期
                    if (!StringUtils.isEmpty(returnVo.getC31())) {
                        Date contractAppointDate = DateUtil.parseDate(returnVo.getC31());
                        orderDetail.setContractAppointDate(contractAppointDate);
                    }
                    //跟踪日期
                    if (!StringUtils.isEmpty(returnVo.getC32())) {
                        Date trackDate = DateUtil.parseDate(returnVo.getC32());
                        orderDetail.setTrackDate(trackDate);
                    }
                } catch (Exception e) {
                    //do nothing
                }
                //采购单价
                if (!StringUtils.isEmpty(returnVo.getC33())) {
                    orderDetail.setCost(new BigDecimal(returnVo.getC33()));
                }

                //发运行接收数量
                BigDecimal quantityReceived = new BigDecimal(returnVo.getC17());
                //PAM的PO数量
                BigDecimal orderNum = orderDetail.getOrderNum() != null ? orderDetail.getOrderNum() : BigDecimal.ZERO;
                //ERP的PO数量
                BigDecimal erpOrderNum = StringUtils.hasText(returnVo.getC25()) ? new BigDecimal(returnVo.getC25()) : BigDecimal.ZERO;

                //行取消状态
                if ("Y".equals(returnVo.getC15())) {
                    orderDetail.setStatus(PurchaseOrderDetailStatus.ORDER_CANCELED.code());
                    //取消数量
                    orderDetail.setCancelNum(orderDetail.getOrderNum());
                } else if (!"OPEN".equals(returnVo.getC14())) {
                    //不是打开状态
                    orderDetail.setStatus(PurchaseOrderDetailStatus.ORDER_CLOSED.code());
                    if (("CLOSED FOR RECEIVING".equals(returnVo.getC14()) || "CLOSED".equals(returnVo.getC14())) && !StringUtils.isEmpty(returnVo.getC17())) {
                        /**
                         * 标识为初始化的订单
                         * 1.当PAM的PO数量<ERP的PO数量时，接收关闭状态下：erp行数量-ERP接收数量，大于等于PAM的PO数量时，赋值PO数量；erp行数量-ERP接收数量，小于PAM的PO数量时，赋值计算结果
                         * 2.当PAM的PO数量>=ERP的PO数量时，与非初始化保持一致（PAM订单数量-ERP接收数量）
                         */
                        if (Objects.equals(purchaseOrder.getInitFlag(), 1) && orderNum.compareTo(erpOrderNum) < 0) {
                            orderDetail.setCancelNum(erpOrderNum.subtract(quantityReceived).min(orderNum));
                        } else {
                            orderDetail.setCancelNum(orderNum.subtract(quantityReceived));
                        }
                        orderDetail.setQuantity(erpOrderNum);
                    }
                } else {
                    //打开状态
                    orderDetail.setStatus(PurchaseOrderDetailStatus.ORDER_PLACED.code());
                    /**
                     * 非初始化订单：取消数量=PAM订单数量-ERP订单数量，记录(更新)ERP的PO数量
                     *
                     * 初始化订单：取消数量不变
                     */
                    if (!Objects.equals(purchaseOrder.getInitFlag(), 1)) {
                        orderDetail.setCancelNum(orderNum.subtract(erpOrderNum));
                        orderDetail.setQuantity(erpOrderNum);
                    }
                }
                // 更新合并行的取消数量&采购需求状态
                updateMergeInfo(orderDetail);
                purchaseOrderDetailMapper.updateByPrimaryKeySelective(orderDetail);
                purchaseOrderMapper.updateByPrimaryKeySelective(purchaseOrder);
                //如果有取消数量则更新采购需求状态
                if (orderDetail.getCancelNum() != null
                        && BigDecimal.ZERO.compareTo(orderDetail.getCancelNum()) != 0) {
                    purchaseMaterialRequirementService.updateStatus(orderDetail.getMaterialPurchaseRequirementId());
                }
            }
        }

    }

    private void updateMergeInfo(PurchaseOrderDetail orderDetail) {
        if (!Objects.equals(orderDetail.getMergeRows(), Boolean.TRUE)) {
            return;
        }
        // 合并行数据
        PurchaseOrderMergeExample mergeExample = new PurchaseOrderMergeExample();
        mergeExample.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE)
                .andPurchaseOrderIdEqualTo(orderDetail.getId());
        mergeExample.setOrderByClause(" create_at desc ");
        List<PurchaseOrderMerge> mergeList = purchaseOrderMergeMapper.selectByExample(mergeExample);
        if (ListUtils.isEmpty(mergeList)) {
            return;
        }
        // 采购订单行取消数量
        BigDecimal totalCancelNum = orderDetail.getCancelNum() != null ? orderDetail.getCancelNum() : BigDecimal.ZERO;
        Integer status = orderDetail.getStatus();
        for (PurchaseOrderMerge purchaseOrderMerge : mergeList) {
            purchaseOrderMerge.setCost(orderDetail.getCost()); //采购单价
            purchaseOrderMerge.setCancelNum(BigDecimal.ZERO);
            purchaseOrderMerge.setStatus(status);
            if (totalCancelNum.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal cancelNum = purchaseOrderMerge.getOrderNum() != null ? purchaseOrderMerge.getOrderNum() : BigDecimal.ZERO;
                ;
                if (totalCancelNum.compareTo(cancelNum) < 0) {
                    cancelNum = totalCancelNum;
                }
                totalCancelNum = totalCancelNum.subtract(cancelNum);
                purchaseOrderMerge.setCancelNum(cancelNum);
            }
            purchaseOrderMergeMapper.updateByPrimaryKeySelective(purchaseOrderMerge);
        }
    }

    @Override
    public void asynDataFromErp(final String date) {
        String startDateTime = date;
        String endDateTime = null;
        if (StringUtils.isEmpty(startDateTime)) {
            endDateTime = DateUtils.format(new Date(), DateUtils.FORMAT_LONG);//当前时间
            startDateTime = com.midea.mcomponent.core.util.date.DateUtil.addDay(endDateTime, -2);//当前时间-2天
        } else {
            //指定的开始时间+1小时
            endDateTime = com.midea.mcomponent.core.util.date.DateUtil.addDay(startDateTime, 1);
        }
        final List<OrganizationRel> rels = getValidOrganization();
        if (ListUtils.isEmpty(rels)) return;
        for (OrganizationRel rel : rels) {
            if (Objects.equals(rel.getOrganizationId(), rel.getOperatingUnitId())) {
                continue;
            }
            final Map<String, String> params = new HashMap();
//            params.put(EsbConstant.ERP_IP_P01, String.valueOf(rel.getOperatingUnitId()));
//            params.put(EsbConstant.ERP_IP_P03, startDateTime);
//            params.put(EsbConstant.ERP_IP_P04, endDateTime);
            params.put(EsbConstant.ERP_SDP_P01, String.valueOf(rel.getOperatingUnitId()));
            params.put(EsbConstant.ERP_SDP_P03, startDateTime);
            params.put(EsbConstant.ERP_SDP_P04, endDateTime);
//            List<ERPMassQueryReturnVo> returnItemList = esbService.callEsbMassQuery(EsbConstant.ERP_PIFACECODE_044, params);
            List<SdpTradeResultResponseEleDto> returnItemList = sdpCarrierServicel.callSdpMassQuery(EsbConstant.ERP_PIFACECODE_044, params);
            if (ListUtils.isEmpty(returnItemList)) {
                continue;
            }
            for (SdpTradeResultResponseEleDto returnVo : returnItemList) {
                String podType = returnVo.getC18(); //发运行处理类型
                //自动同步只处理ERP接口pod_type字段为空的数据
                if (StringUtil.isNotNull(podType)) {
                    continue;
                }
                //头数据
                PurchaseOrder purchaseOrder = new PurchaseOrder();
                if (!StringUtils.isEmpty(returnVo.getC30())) {
                    //发布状态
                    purchaseOrder.setErpOrderStatus(ErpOrderStatus.getCode(returnVo.getC30()));
                }
                //初始化
                purchaseOrder.setId(Long.valueOf(returnVo.getC2()));
                purchaseOrder.setNum(returnVo.getC3());//订单编号
                purchaseOrder.setVendorName(returnVo.getC23());
                purchaseOrder.setVendorSiteCode(returnVo.getC24());//供应商地点
                purchaseOrder.setDeletedFlag(Boolean.FALSE);
                purchaseOrder.setUpdateAt(new Date());
                purchaseOrder.setOrderStatus(1);
                purchaseOrder.setSyncStatus(1);
                purchaseOrder.setErpOrderStatus(ErpOrderStatus.getCode(returnVo.getC30()));
                purchaseOrder.setOuId(rel.getOperatingUnitId());
                //头取消状态
                if ("Y".equals(returnVo.getC6())) {
                    purchaseOrder.setOrderStatus(PurchaseOrderStatus.CANCELED.code());
                } else if ("Y".equals(returnVo.getC7())) {
                    //冻结状态
                    purchaseOrder.setOrderStatus(PurchaseOrderStatus.FROZEN.code());
                } else if ("Y".equals(returnVo.getC8())) {
                    //暂挂状态
                    purchaseOrder.setOrderStatus(PurchaseOrderStatus.ONHOLD.code());
                } else if (!"OPEN".equals(returnVo.getC5())) {
                    //不是打开状态
                    purchaseOrder.setOrderStatus(PurchaseOrderStatus.CLOSED.code());
                }
                Integer lineNum = StringUtils.isEmpty(returnVo.getC10()) ? null : Integer.parseInt(returnVo.getC10());

                //行数据
                PurchaseOrderDetail orderDetail = new PurchaseOrderDetail();
                orderDetail.setPurchaseOrderId(purchaseOrder.getId());
                orderDetail.setLineNumber(lineNum);
                orderDetail.setDeletedFlag(Boolean.FALSE);
                try {
                    //供方承诺交期
                    if (!StringUtils.isEmpty(returnVo.getC31())) {
                        Date contractAppointDate = DateUtil.parseDate(returnVo.getC31());
                        orderDetail.setContractAppointDate(contractAppointDate);
                    }
                    //跟踪日期
                    if (!StringUtils.isEmpty(returnVo.getC32())) {
                        Date trackDate = DateUtil.parseDate(returnVo.getC32());
                        orderDetail.setTrackDate(trackDate);
                    }
                } catch (Exception e) {
                    //do nothing
                }
                //采购单价
                if (!StringUtils.isEmpty(returnVo.getC33())) {
                    orderDetail.setCost(new BigDecimal(returnVo.getC33()));
                }

                //发运行接收数量
                BigDecimal quantityReceived = new BigDecimal(returnVo.getC17());
                //PAM的PO数量
                BigDecimal orderNum = orderDetail.getOrderNum() != null ? orderDetail.getOrderNum() : BigDecimal.ZERO;
                //ERP的PO数量
                BigDecimal erpOrderNum = StringUtils.hasText(returnVo.getC25()) ? new BigDecimal(returnVo.getC25()) : BigDecimal.ZERO;

                //行取消状态
                if ("Y".equals(returnVo.getC15())) {
                    orderDetail.setStatus(PurchaseOrderDetailStatus.ORDER_CANCELED.code());
                    //取消数量
                    orderDetail.setCancelNum(orderDetail.getOrderNum());
                } else if (!"OPEN".equals(returnVo.getC14())) {
                    //不是打开状态
                    orderDetail.setStatus(PurchaseOrderDetailStatus.ORDER_CLOSED.code());
                    if (("CLOSED FOR RECEIVING".equals(returnVo.getC14()) || "CLOSED".equals(returnVo.getC14())) && !StringUtils.isEmpty(returnVo.getC17())) {
                        /**
                         * 标识为初始化的订单
                         * 1.当PAM的PO数量<ERP的PO数量时，接收关闭状态下：erp行数量-ERP接收数量，大于等于PAM的PO数量时，赋值PO数量；erp行数量-ERP接收数量，小于PAM的PO数量时，赋值计算结果
                         * 2.当PAM的PO数量>=ERP的PO数量时，与非初始化保持一致（PAM订单数量-ERP接收数量）
                         */
                        if (Objects.equals(purchaseOrder.getInitFlag(), 1) && orderNum.compareTo(erpOrderNum) < 0) {
                            orderDetail.setCancelNum(erpOrderNum.subtract(quantityReceived).min(orderNum));
                        } else {
                            orderDetail.setCancelNum(orderNum.subtract(quantityReceived));
                        }
                    }
                } else {
                    //打开状态
                    orderDetail.setStatus(PurchaseOrderDetailStatus.ORDER_PLACED.code());
                    /**
                     * 非初始化订单：取消数量=PAM订单数量-ERP订单数量，记录(更新)ERP的PO数量
                     *
                     * 初始化订单：取消数量不变
                     */
                    if (!Objects.equals(purchaseOrder.getInitFlag(), 1)) {
                        orderDetail.setCancelNum(orderNum.subtract(erpOrderNum));
                        orderDetail.setQuantity(erpOrderNum);
                    }
                }
                //项目号			project_num
                if (StringUtils.isNotEmpty(returnVo.getC19())) {
                    orderDetail.setProjectNum(returnVo.getC19());
                }
                //物料交货时间			need_by_date
                if (StringUtils.isNotEmpty(returnVo.getC20())) {
                    orderDetail.setNeedByDate(returnVo.getC20());
                }
                //入库数量			delivered_quantity
                if (StringUtils.isNotEmpty(returnVo.getC21())) {
                    orderDetail.setDeliveredQuantity(new BigDecimal(returnVo.getC21()));
                }
                //供应商编码			vendor_num
                if (StringUtils.isNotEmpty(returnVo.getC22())) {
                    orderDetail.setVendorNum(returnVo.getC22());
                }
                //供应商名称			vendor_name
                if (StringUtils.isNotEmpty(returnVo.getC23())) {
                    orderDetail.setVendorName(returnVo.getC23());
                }
                //供应商地点			vendor_site_code
                if (StringUtils.isNotEmpty(returnVo.getC24())) {
                    orderDetail.setVendorSiteCode(returnVo.getC24());
                }
                //记录到记录表
                PurchaseOrderRecord record = new PurchaseOrderRecord();
                //订单行数量			quantity
                if (StringUtils.isNotEmpty(returnVo.getC25())) {
                    orderDetail.setQuantity(new BigDecimal(returnVo.getC25()));
                    record.setQuantity(new BigDecimal(returnVo.getC25()));
                }

                //创建日期
                if (StringUtils.isNotEmpty(returnVo.getC26())) {
                    Date createAt = DateUtils.parse(returnVo.getC26(), DateUtils.FORMAT_SHORT);
                    purchaseOrder.setCreateAt(createAt);
                    orderDetail.setCreateAt(createAt);
                    record.setCreateAt(createAt);
                }
                //汇率
                if (StringUtils.isNotEmpty(returnVo.getC27())) {
                    purchaseOrder.setConversionRate(new BigDecimal(returnVo.getC27()));
                    record.setConversionRate(new BigDecimal(returnVo.getC27()));
                }
                //币种
                if (StringUtils.isNotEmpty(returnVo.getC28())) {
                    purchaseOrder.setCurrency(returnVo.getC28());
                    record.setCurrency(returnVo.getC28());
                }
                //发运行单价
//                if (StringUtils.isNotEmpty(returnVo.getC29())) {
//                    orderDetail.setCost(new BigDecimal(returnVo.getC29()));
//                    record.setCost(new BigDecimal(returnVo.getC29()));
//                }

                if ("MH1".equals(rel.getOrganizationCode())) {
                    purchaseOrder.setSource("erp");
                    purchaseOrder.setSyncSourceSystem("PAM");
                    purchaseOrderMapper.insertSelective(purchaseOrder);
                }

                if (StringUtils.isEmpty(orderDetail.getErpCode())) {
                    orderDetail.setErpCode(returnVo.getC12());
                }
                if (StringUtils.isNotEmpty(returnVo.getC10())) {
                    orderDetail.setLineNumber(Integer.valueOf(returnVo.getC10()));
                }
                purchaseOrderDetailMapper.insertSelective(orderDetail);

                if (StringUtils.isNotEmpty(returnVo.getC1())) {
                    record.setOrgId(Long.valueOf(returnVo.getC1()));

                }
                if (StringUtils.isNotEmpty(returnVo.getC2())) {
                    record.setPoHeaderId(Long.valueOf(returnVo.getC2()));
                }
                if (StringUtils.isNotEmpty(returnVo.getC9())) {
                    record.setPoLineId(Long.valueOf(returnVo.getC9()));
                }

                //预先更新字段
                if (StringUtils.isNotEmpty(returnVo.getC4())) {
                    record.setAuthorizationStatus(returnVo.getC4());
                }
                if (StringUtils.isNotEmpty(returnVo.getC5())) {
                    record.setClosedCode(returnVo.getC5());
                }
                if (StringUtils.isNotEmpty(returnVo.getC6())) {
                    record.setCancelFlag(returnVo.getC6());
                }
                if (StringUtils.isNotEmpty(returnVo.getC7())) {
                    record.setFrozenFlag(returnVo.getC7());
                }
                if (StringUtils.isNotEmpty(returnVo.getC8())) {
                    record.setUserHoldFlag(returnVo.getC8());
                }
                if (StringUtils.isNotEmpty(returnVo.getC14())) {
                    record.setClosedCode2(returnVo.getC14());
                }
                if (StringUtils.isNotEmpty(returnVo.getC15())) {
                    record.setCancelFlag2(returnVo.getC15());
                }
                //留意部位时间格式
                if (StringUtils.isNotEmpty(returnVo.getC16())) {
                    if (returnVo.getC16().length() > 10) {
                        record.setLastUpdateDate(DateUtils.parse(returnVo.getC16()));
                    } else {
                        record.setLastUpdateDate(DateUtils.parse(returnVo.getC16(), DateUtils.FORMAT_SHORT));
                    }
                }
                if (StringUtils.isNotEmpty(returnVo.getC17())) {
                    record.setQuantityReceived(new BigDecimal(returnVo.getC17()));
                }
                if (StringUtils.isNotEmpty(returnVo.getC19())) {
                    record.setProjectNum(returnVo.getC19());
                }
                if (StringUtils.isNotEmpty(returnVo.getC20())) {
                    record.setNeedByDate(returnVo.getC20());
                }
                if (StringUtils.isNotEmpty(returnVo.getC21())) {
                    record.setDeliveredQuantity(new BigDecimal(returnVo.getC21()));
                }

                if (null != record.getOrgId() && null != record.getPoHeaderId() && null != record.getPoLineId()) {
                    PurchaseOrderRecordExample recordExample = new PurchaseOrderRecordExample();
                    recordExample.createCriteria().andOrgIdEqualTo(record.getOrgId()).andPoHeaderIdEqualTo(record.getPoHeaderId()).andPoLineIdEqualTo(record.getPoLineId());
                    List<PurchaseOrderRecord> list = purchaseOrderRecordMapper.selectByExample(recordExample);
                    //变更
                    if (ListUtils.isNotEmpty(list)) {
                        PurchaseOrderRecord r = list.get(0);
                        record.setId(r.getId());
                        record.setUpdateAt(new Date());
                        purchaseOrderRecordMapper.updateByPrimaryKeySelective(record);
                    } else {
                        if (StringUtils.isNotEmpty(returnVo.getC3())) {
                            record.setPoNumber(returnVo.getC3());
                        }

                        if (StringUtils.isNotEmpty(returnVo.getC10())) {
                            record.setLineNum(Long.valueOf(returnVo.getC10()));
                        }
                        if (StringUtils.isNotEmpty(returnVo.getC11())) {
                            record.setItemId(Long.valueOf(returnVo.getC11()));
                        }
                        if (StringUtils.isNotEmpty(returnVo.getC12())) {
                            record.setItemCode(returnVo.getC12());
                        }
                        if (StringUtils.isNotEmpty(returnVo.getC13())) {
                            record.setLineLocationId(Long.valueOf(returnVo.getC13()));
                        }

                        if (StringUtils.isNotEmpty(returnVo.getC18())) {
                            record.setPodType(returnVo.getC18());
                        }

                        if (StringUtils.isNotEmpty(returnVo.getC22())) {
                            record.setVendorNum(returnVo.getC22());
                        }
                        if (StringUtils.isNotEmpty(returnVo.getC23())) {
                            record.setVendorName(returnVo.getC23());
                        }
                        if (StringUtils.isNotEmpty(returnVo.getC24())) {
                            record.setVendorSiteCode(returnVo.getC24());
                        }
                        if (StringUtils.isNotEmpty(returnVo.getC25())) {
                            record.setQuantity(new BigDecimal(returnVo.getC25()));
                        }
                        purchaseOrderRecordMapper.insert(record);
                        if (Objects.equals("erp", purchaseOrder.getSource())
                                && !StringUtils.isEmpty(record.getItemCode())
                                && StringUtils.isEmpty(orderDetail.getErpCode())) {
                            //查询物料信息
                            MaterialDto materialDto = materialExtService.invokeMaterialApiGetByErpCodeAndOuId(record.getItemCode(),
                                    record.getOrgId());
                            if (null != materialDto) {
                                orderDetail.setErpCode(materialDto.getErpCode());
                                orderDetail.setPamCode(materialDto.getPamCode());
                                orderDetail.setMaterielId(materialDto.getId());
                                orderDetail.setMaterielDescr(materialDto.getItemInfo());
                                orderDetail.setUnitCode(materialDto.getUnit());
                                orderDetail.setUnit(materialDto.getUnitName());
                                purchaseOrderDetailMapper.updateByPrimaryKeySelective(orderDetail);
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 获取系统有效的库存组织.
     *
     * @return 有效的库存组织
     */
    private List<OrganizationRel> getValidOrganization() {
        final String url = StringUtils.buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/organizationRel/getValidOrganization", new HashMap<>());
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        final DataResponse<List<OrganizationRel>> data = JSON.parseObject(res, new TypeReference<DataResponse<List<OrganizationRel>>>() {
        });
        return data != null ? data.getData() : null;
    }


    @Override
    public Boolean pushToErp(Long id, Long userBy) {
        Asserts.notEmpty(id, ErrorCode.IDS_NOT_NULL);
        PurchaseOrder purchaseOrder = purchaseOrderMapper.selectByPrimaryKey(id);
        Asserts.notEmpty(purchaseOrder, ErrorCode.CTC_ORDER_NOT_NULL);
        Asserts.notEmpty(purchaseOrder.getOuId(), ErrorCode.CTC_OU_ID_NOT_NULL);

        Date endDateTime = DateUtil.getEndingOfDay(new Date());//当前时间
        Date startDateTime = purchaseOrder.getCreateAt();
        if (startDateTime == null) {
            startDateTime = com.midea.mcomponent.core.util.date.DateUtil.addYear(endDateTime, -1);//当前时间-1年
        }

        //行数据
        PurchaseOrderDetailExample example = new PurchaseOrderDetailExample();
        example.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andPurchaseOrderIdEqualTo(id);
        List<PurchaseOrderDetail> orderDetails = purchaseOrderDetailMapper.selectByExample(example);
        Asserts.notEmpty(orderDetails, ErrorCode.CTC_ORDER_DETAILS_ORDER_NUM_NULL);

        Map<Integer, PurchaseOrderDetail> orderDetailMap = new HashMap<>();
        for (PurchaseOrderDetail orderDetail : orderDetails) {
            orderDetailMap.put(orderDetail.getLineNumber(), orderDetail);
        }

        final Map<String, String> params = new HashMap();
//        params.put(EsbConstant.ERP_IP_P01, String.valueOf(purchaseOrder.getOuId()));
//        params.put(EsbConstant.ERP_IP_P02, purchaseOrder.getNum());
//        params.put(EsbConstant.ERP_IP_P03, DateUtil.format(startDateTime, DateUtil.TIMESTAMP_PATTERN));
//        params.put(EsbConstant.ERP_IP_P04, DateUtil.format(endDateTime, DateUtil.TIMESTAMP_PATTERN));
        params.put(EsbConstant.ERP_SDP_P01, String.valueOf(purchaseOrder.getOuId()));
        params.put(EsbConstant.ERP_SDP_P02, purchaseOrder.getNum());
        params.put(EsbConstant.ERP_SDP_P03, DateUtil.format(startDateTime, DateUtil.TIMESTAMP_PATTERN));
        params.put(EsbConstant.ERP_SDP_P04, DateUtil.format(endDateTime, DateUtil.TIMESTAMP_PATTERN));
//        List<ERPMassQueryReturnVo> returnItemList = esbService.callEsbMassQuery(EsbConstant.ERP_PIFACECODE_044, params);
        List<SdpTradeResultResponseEleDto> returnItemList = sdpCarrierServicel.callSdpMassQuery(EsbConstant.ERP_PIFACECODE_044, params);
        for (SdpTradeResultResponseEleDto returnVo : returnItemList) {
            if (!StringUtils.isEmpty(returnVo.getC30())) {
                //发布状态
                purchaseOrder.setErpOrderStatus(ErpOrderStatus.getCode(returnVo.getC30()));
            }

            //行数据
            Integer lineNum = Integer.parseInt(returnVo.getC10());
            PurchaseOrderDetail orderDetail = orderDetailMap.get(lineNum);
            if (orderDetail == null) {
                continue;
            }
            try {
                //供方承诺交期
                if (!StringUtils.isEmpty(returnVo.getC31())) {
                    Date contractAppointDate = DateUtil.parseDate(returnVo.getC31());
                    orderDetail.setContractAppointDate(contractAppointDate);
                }
                //跟踪日期
                if (!StringUtils.isEmpty(returnVo.getC32())) {
                    Date trackDate = DateUtil.parseDate(returnVo.getC32());
                    orderDetail.setTrackDate(trackDate);
                }
            } catch (Exception e) {
                //do nothing
            }
            //采购单价
            if (!StringUtils.isEmpty(returnVo.getC33())) {
                orderDetail.setCost(new BigDecimal(returnVo.getC33()));
            }

            //发运行接收数量
            BigDecimal quantityReceived = new BigDecimal(returnVo.getC17());
            //PAM的PO数量
            BigDecimal orderNum = orderDetail.getOrderNum() != null ? orderDetail.getOrderNum() : BigDecimal.ZERO;
            //ERP的PO数量
            BigDecimal erpOrderNum = StringUtils.hasText(returnVo.getC25()) ? new BigDecimal(returnVo.getC25()) : BigDecimal.ZERO;

            //行取消状态
            if ("Y".equals(returnVo.getC15())) {
                orderDetail.setStatus(PurchaseOrderDetailStatus.ORDER_CANCELED.code());
                //取消数量
                orderDetail.setCancelNum(orderDetail.getOrderNum());
            } else if (!"OPEN".equals(returnVo.getC14())) {
                //不是打开状态
                orderDetail.setStatus(PurchaseOrderDetailStatus.ORDER_CLOSED.code());
                if (("CLOSED FOR RECEIVING".equals(returnVo.getC14()) || "CLOSED".equals(returnVo.getC14())) && !StringUtils.isEmpty(returnVo.getC17())) {
                    /**
                     * 标识为初始化的订单
                     * 1.当PAM的PO数量<ERP的PO数量时，接收关闭状态下：erp行数量-ERP接收数量，大于等于PAM的PO数量时，赋值PO数量；erp行数量-ERP接收数量，小于PAM的PO数量时，赋值计算结果
                     * 2.当PAM的PO数量>=ERP的PO数量时，与非初始化保持一致（PAM订单数量-ERP接收数量）
                     */
                    if (Objects.equals(purchaseOrder.getInitFlag(), 1) && orderNum.compareTo(erpOrderNum) < 0) {
                        orderDetail.setCancelNum(erpOrderNum.subtract(quantityReceived).min(orderNum));
                    } else {
                        orderDetail.setCancelNum(orderNum.subtract(quantityReceived));
                    }
                    orderDetail.setQuantity(erpOrderNum);
                }
            } else {
                //打开状态
                orderDetail.setStatus(PurchaseOrderDetailStatus.ORDER_PLACED.code());
                /**
                 * 非初始化订单：取消数量=PAM订单数量-ERP订单数量，记录(更新)ERP的PO数量
                 *
                 * 初始化订单：取消数量不变
                 */
                if (!Objects.equals(purchaseOrder.getInitFlag(), 1)) {
                    orderDetail.setCancelNum(orderNum.subtract(erpOrderNum));
                    orderDetail.setQuantity(erpOrderNum);
                }
            }
            //头取消状态
            if ("Y".equals(returnVo.getC6())) {
                purchaseOrder.setOrderStatus(PurchaseOrderStatus.CANCELED.code());
            } else if ("Y".equals(returnVo.getC7())) {
                //冻结状态
                purchaseOrder.setOrderStatus(PurchaseOrderStatus.FROZEN.code());
            } else if ("Y".equals(returnVo.getC8())) {
                //暂挂状态
                purchaseOrder.setOrderStatus(PurchaseOrderStatus.ONHOLD.code());
            } else if (!"OPEN".equals(returnVo.getC5())) {
                //不是打开状态
                purchaseOrder.setOrderStatus(PurchaseOrderStatus.CLOSED.code());
            }
            // 更新合并行的取消数量&采购需求状态
            updateMergeInfo(orderDetail);
            purchaseOrderDetailMapper.updateByPrimaryKeySelective(orderDetail);
            purchaseOrderMapper.updateByPrimaryKeySelective(purchaseOrder);
            //如果有取消数量则更新采购需求状态
            if (orderDetail.getCancelNum() != null
                    && BigDecimal.ZERO.compareTo(orderDetail.getCancelNum()) != 0) {
                purchaseMaterialRequirementService.updateStatus(orderDetail.getMaterialPurchaseRequirementId());
            }
        }
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean pushToErpSync(Long id, Long userBy) {
        applicationEventPublisher.publishEvent(new PushToErpSyncEvent(this, id, userBy));
        return true;
    }

    @Override
    public Boolean pushToErpChange(Long id, Long userBy) {

        String lockName = String.format("pushToErpChange_requirementIdList");
        String currentDateStr = DateUtil.format(new Date(), "yyyyMMdd");
        try {
            if (DistributedCASLock.lock(lockName, currentDateStr, MAX_WAIT_TIME, MAX_WAIT_TIME * 2)) {
                logger.info("加锁成功,采购订单同步,锁名称为:{},时间为:{}", lockName, currentDateStr);
                Asserts.notEmpty(id, ErrorCode.IDS_NOT_NULL);
                PurchaseOrder purchaseOrder = purchaseOrderMapper.selectByPrimaryKey(id);
                Asserts.notEmpty(purchaseOrder, ErrorCode.CTC_ORDER_NOT_NULL);
                Asserts.notEmpty(purchaseOrder.getOuId(), ErrorCode.CTC_OU_ID_NOT_NULL);

                Date endDateTime = DateUtil.getEndingOfDay(new Date());//当前时间
                Date startDateTime = purchaseOrder.getCreateAt();
                if (startDateTime == null) {
                    startDateTime = com.midea.mcomponent.core.util.date.DateUtil.addYear(endDateTime, -1);//当前时间-1年
                }

                //行数据
                PurchaseOrderDetailExample example = new PurchaseOrderDetailExample();
                example.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andPurchaseOrderIdEqualTo(id);
                List<PurchaseOrderDetail> orderDetails = purchaseOrderDetailMapper.selectByExample(example);
                Asserts.notEmpty(orderDetails, ErrorCode.CTC_ORDER_DETAILS_ORDER_NUM_NULL);

                Map<Integer, PurchaseOrderDetail> orderDetailMap = new HashMap<>();
                for (PurchaseOrderDetail orderDetail : orderDetails) {
                    orderDetailMap.put(orderDetail.getLineNumber(), orderDetail);
                }
                final Map<String, String> params = new HashMap();
//                params.put(EsbConstant.ERP_IP_P01, String.valueOf(purchaseOrder.getOuId()));
//                params.put(EsbConstant.ERP_IP_P02, purchaseOrder.getNum());
//                params.put(EsbConstant.ERP_IP_P03, DateUtil.format(startDateTime, DateUtil.TIMESTAMP_PATTERN));
//                params.put(EsbConstant.ERP_IP_P04, DateUtil.format(endDateTime, DateUtil.TIMESTAMP_PATTERN));
                params.put(EsbConstant.ERP_SDP_P01, String.valueOf(purchaseOrder.getOuId()));
                params.put(EsbConstant.ERP_SDP_P02, purchaseOrder.getNum());
                params.put(EsbConstant.ERP_SDP_P03, DateUtil.format(startDateTime, DateUtil.TIMESTAMP_PATTERN));
                params.put(EsbConstant.ERP_SDP_P04, DateUtil.format(endDateTime, DateUtil.TIMESTAMP_PATTERN));
//                List<ERPMassQueryReturnVo> returnItemList = esbService.callEsbMassQuery(EsbConstant.ERP_PIFACECODE_044, params);
                List<SdpTradeResultResponseEleDto> returnItemList = sdpCarrierServicel.callSdpMassQuery(EsbConstant.ERP_PIFACECODE_044, params);
                List<Long> list = new ArrayList<>();
                for (SdpTradeResultResponseEleDto returnVo : returnItemList) {
                    if (!StringUtils.isEmpty(returnVo.getC30())) {
                        //发布状态
                        purchaseOrder.setErpOrderStatus(ErpOrderStatus.getCode(returnVo.getC30()));
                    }

                    //行数据
                    Integer lineNum = Integer.parseInt(returnVo.getC10());
                    PurchaseOrderDetail orderDetail = orderDetailMap.get(lineNum);
                    if (orderDetail == null) {
                        continue;
                    }
                    try {
                        //供方承诺交期
                        if (!StringUtils.isEmpty(returnVo.getC31())) {
                            Date contractAppointDate = DateUtil.parseDate(returnVo.getC31());
                            orderDetail.setContractAppointDate(contractAppointDate);
                        }
                        //跟踪日期
                        if (!StringUtils.isEmpty(returnVo.getC32())) {
                            Date trackDate = DateUtil.parseDate(returnVo.getC32());
                            orderDetail.setTrackDate(trackDate);
                        }
                    } catch (Exception e) {
                        //do nothing
                    }
                    //采购单价
                    if (!StringUtils.isEmpty(returnVo.getC33())) {
                        orderDetail.setCost(new BigDecimal(returnVo.getC33()));
                    }

                    //发运行接收数量
                    BigDecimal quantityReceived = new BigDecimal(returnVo.getC17());
                    //PAM的PO数量
                    BigDecimal orderNum = orderDetail.getOrderNum() != null ? orderDetail.getOrderNum() : BigDecimal.ZERO;
                    //ERP的PO数量
                    BigDecimal erpOrderNum = StringUtils.hasText(returnVo.getC25()) ? new BigDecimal(returnVo.getC25()) : BigDecimal.ZERO;

                    //行取消状态
                    if ("Y".equals(returnVo.getC15())) {
                        orderDetail.setStatus(PurchaseOrderDetailStatus.ORDER_CANCELED.code());
                        //取消数量
                        orderDetail.setCancelNum(orderDetail.getOrderNum());
                    } else if (!"OPEN".equals(returnVo.getC14())) {
                        //不是打开状态
                        orderDetail.setStatus(PurchaseOrderDetailStatus.ORDER_CLOSED.code());
                        if (("CLOSED FOR RECEIVING".equals(returnVo.getC14()) || "CLOSED".equals(returnVo.getC14())) && !StringUtils.isEmpty(returnVo.getC17())) {
                            /**
                             * 标识为初始化的订单
                             * 1.当PAM的PO数量<ERP的PO数量时，接收关闭状态下：erp行数量-ERP接收数量，大于等于PAM的PO数量时，赋值PO数量；erp行数量-ERP接收数量，小于PAM的PO数量时，赋值计算结果
                             * 2.当PAM的PO数量>=ERP的PO数量时，与非初始化保持一致（PAM订单数量-ERP接收数量）
                             */
                            if (Objects.equals(purchaseOrder.getInitFlag(), 1) && orderNum.compareTo(erpOrderNum) < 0) {
                                orderDetail.setCancelNum(erpOrderNum.subtract(quantityReceived).min(orderNum));
                            } else {
                                orderDetail.setCancelNum(orderNum.subtract(quantityReceived));
                            }
                            orderDetail.setQuantity(erpOrderNum);
                        }
                    } else {
                        //打开状态
                        orderDetail.setStatus(PurchaseOrderDetailStatus.ORDER_PLACED.code());
                        /**
                         * 非初始化订单：取消数量=PAM订单数量-ERP订单数量，记录(更新)ERP的PO数量
                         *
                         * 初始化订单：取消数量不变
                         */
                        if (!Objects.equals(purchaseOrder.getInitFlag(), 1)) {
                            orderDetail.setCancelNum(orderNum.subtract(erpOrderNum));
                            orderDetail.setQuantity(erpOrderNum);
                        }
                    }
                    //头取消状态
                    if ("Y".equals(returnVo.getC6())) {
                        purchaseOrder.setOrderStatus(PurchaseOrderStatus.CANCELED.code());
                    } else if ("Y".equals(returnVo.getC7())) {
                        //冻结状态
                        purchaseOrder.setOrderStatus(PurchaseOrderStatus.FROZEN.code());
                    } else if ("Y".equals(returnVo.getC8())) {
                        //暂挂状态
                        purchaseOrder.setOrderStatus(PurchaseOrderStatus.ONHOLD.code());
                    } else if (!"OPEN".equals(returnVo.getC5())) {
                        //不是打开状态
                        purchaseOrder.setOrderStatus(PurchaseOrderStatus.CLOSED.code());
                    }
                    // 更新合并行的取消数量&采购需求状态
                    updateMergeInfo(orderDetail);
                    purchaseOrderDetailMapper.updateByPrimaryKeySelective(orderDetail);
                    purchaseOrderMapper.updateByPrimaryKeySelective(purchaseOrder);
                    //如果有取消数量则更新采购需求状态

                    if (orderDetail.getCancelNum() != null
                            && BigDecimal.ZERO.compareTo(orderDetail.getCancelNum()) != 0) {
                        //purchaseMaterialRequirementService.updateStatus(orderDetail.getMaterialPurchaseRequirementId());
                        list.add(orderDetail.getMaterialPurchaseRequirementId());
                    }
                }
                if (CollectionUtils.isNotEmpty(list)) {
                    logger.info("获取待更新的数据为:{}", JSON.toJSONString(list));
                    purchaseMaterialRequirementService.batchUpdateRequirementStatus(list);
                }
            }
        } catch (Exception e) {
            logger.error("采购订单同步加锁失败", e);
            throw e;
        } finally {
            DistributedCASLock.unLock(lockName, currentDateStr);
        }
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean resendToErp(Long id, Long userBy) {
        Asserts.notEmpty(id, ErrorCode.ID_NOT_NULL);
        PurchaseOrder purchaseOrder = purchaseOrderMapper.selectByPrimaryKey(id);
        Asserts.notEmpty(purchaseOrder, ErrorCode.CTC_ORDER_NOT_NULL);
        Asserts.notEquals(purchaseOrder.getSyncStatus(), PurchaseOrderSyncStatus.IN_SYNC.code(),
                ErrorCode.CTC_ORDER_UN_PUSH);
        Asserts.notEquals(purchaseOrder.getSyncStatus(), PurchaseOrderSyncStatus.SYNCED.code(),
                ErrorCode.CTC_ORDER_UN_PUSH);
        Asserts.notEquals(purchaseOrder.getSyncStatus(), PurchaseOrderSyncStatus.CHANGE_IN_SYNC.code(),
                ErrorCode.CTC_ORDER_UN_PUSH);
        if (Objects.equals(purchaseOrder.getSyncStatus(), PurchaseOrderSyncStatus.CHANGE_FAILED_SYNC.code())) {
            resendChangeOrderToErp(id);
        } else {
            resendOrderToErp(id);
        }
        return true;
    }

    private void resendChangeOrderToErp(Long purchaseOrderId) {
        // 查询最新的变更记录
        Long recordId = purchaseOrderExtMapper.getLatestChangRecordId(purchaseOrderId);
        Asserts.notEmpty(recordId, ErrorCode.CTC_CHANGE_RECORD_NOT_FIND);
        //先把状态改为"同步中"
        PurchaseOrder purchaseOrder = new PurchaseOrder();
        purchaseOrder.setId(purchaseOrderId);
        purchaseOrder.setSyncStatus(PurchaseOrderSyncStatus.CHANGE_IN_SYNC.code());
        purchaseOrder.setErpMessage(null);
        purchaseOrderMapper.updateByPrimaryKeySelective(purchaseOrder);

        //把resend表数据改为"同步中"
        final ResendExecuteExample condition = new ResendExecuteExample();
        condition.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE)
                .andApplyNoEqualTo(Long.toString(purchaseOrderId))
                .andSubApplyNoEqualTo(Long.toString(recordId))
                .andBusinessTypeEqualTo(BusinessTypeEnums.PURCHASE_ORDER_CHANGE.getCode());
        final List<ResendExecute> resends = resendExecuteService.selectByExample(condition);
        if (ListUtils.isNotEmpty(resends)) {
            resends.forEach(r -> {
                r.setStatus((CommonStatus.PROCESSING.getCode()));
                resendExecuteService.updateByPrimaryKeySelective(r);
            });
        }
        agencySynService.syndataToErp(BusinessTypeEnums.PURCHASE_ORDER_CHANGE.getCode(), purchaseOrderId);

        AsyncRequestResult asyncCostCollectionResult = new AsyncRequestResult();
        asyncRequestResultService.add(asyncCostCollectionResult);

        //防止超时，异步向第三方推送数据
        applicationEventPublisher.publishEvent(new PurchaseOrderChangeSyncEvent(this, Long.toString(purchaseOrderId),
                Long.toString(recordId), BusinessTypeEnums.PURCHASE_ORDER_CHANGE.getCode(), resends));
    }

    private void resendOrderToErp(Long purchaseOrderId) {
        //先把状态改为"同步中"
        PurchaseOrder purchaseOrder = new PurchaseOrder();
        purchaseOrder.setId(purchaseOrderId);
        purchaseOrder.setSyncStatus(PurchaseOrderSyncStatus.IN_SYNC.code());
        purchaseOrder.setErpMessage(null);
        purchaseOrderMapper.updateByPrimaryKeySelective(purchaseOrder);

        //把resend表数据改为"同步中"
        final ResendExecuteExample condition = new ResendExecuteExample();
        condition.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE)
                .andApplyNoEqualTo(Long.toString(purchaseOrderId))
                .andBusinessTypeEqualTo(BusinessTypeEnums.PURCHASE_ORDER.getCode());
        final List<ResendExecute> resends = resendExecuteService.selectByExample(condition);
        if (ListUtils.isNotEmpty(resends)) {
            resends.forEach(r -> {
                r.setStatus((CommonStatus.PROCESSING.getCode()));
                resendExecuteService.updateByPrimaryKeySelective(r);
            });
        }
        agencySynService.syndataToErp(BusinessTypeEnums.PURCHASE_ORDER.getCode(), purchaseOrderId);

        AsyncRequestResult asyncCostCollectionResult = new AsyncRequestResult();
        asyncRequestResultService.add(asyncCostCollectionResult);

        //防止超时，异步向第三方推送数据
        applicationEventPublisher.publishEvent(new PurchaseOrderSyncEvent(this, Long.toString(purchaseOrderId),
                null, BusinessTypeEnums.PURCHASE_ORDER.getCode(), resends));
    }

    @Override
    public Boolean cancelOrder(Long id, Long userBy) {
        Asserts.notEmpty(id, ErrorCode.IDS_NOT_NULL);
        PurchaseOrder purchaseOrder = purchaseOrderMapper.selectByPrimaryKey(id);
        Asserts.notEmpty(purchaseOrder, ErrorCode.CTC_ORDER_NOT_NULL);
        //判断订单是否已取消
        if (PurchaseOrderStatus.CANCELED.code().equals(purchaseOrder.getOrderStatus())) {
            return true;
        }
        //同步失败的订单才可以取消
        if (!PurchaseOrderSyncStatus.FAILED_SYNC.code().equals(purchaseOrder.getSyncStatus())) {
            throw new MipException("同步失败的订单才允许取消");
        }
        //头取消
        purchaseOrder.setOrderStatus(PurchaseOrderStatus.CANCELED.code());
        purchaseOrder.setSyncStatus(PurchaseOrderSyncStatus.NOT_SYNCED.code());//未同步
        purchaseOrderMapper.updateByPrimaryKeySelective(purchaseOrder);
        //行数据
        PurchaseOrderDetailExample example = new PurchaseOrderDetailExample();
        example.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andPurchaseOrderIdEqualTo(id);
        List<PurchaseOrderDetail> orderDetails = purchaseOrderDetailMapper.selectByExample(example);
        Asserts.notEmpty(orderDetails, ErrorCode.CTC_ORDER_DETAILS_ORDER_NUM_NULL);
        //行取消
        List<Long> requirementIdList = new ArrayList<>();
        for (PurchaseOrderDetail orderDetail : orderDetails) {
            orderDetail.setStatus(PurchaseOrderDetailStatus.ORDER_CANCELED.code());
            //取消数量
            orderDetail.setCancelNum(orderDetail.getOrderNum());
            purchaseOrderDetailMapper.updateByPrimaryKeySelective(orderDetail);

            if (Objects.equals(Boolean.TRUE, orderDetail.getMergeRows())) {
                // 删除标识为1的采购订单合并行对应的采购需求同样需要更新状态
                PurchaseOrderMergeExample mergeExample = new PurchaseOrderMergeExample();
                mergeExample.createCriteria().andPurchaseOrderIdEqualTo(orderDetail.getId());
                List<PurchaseOrderMerge> mergeList = purchaseOrderMergeMapper.selectByExample(mergeExample);
                for (PurchaseOrderMerge purchaseOrderMerge : mergeList) {
                    requirementIdList.add(purchaseOrderMerge.getMaterialPurchaseRequirementId());
                }
            } else {
                requirementIdList.add(orderDetail.getMaterialPurchaseRequirementId());
            }
            //更新采购需求状态
            purchaseMaterialRequirementService.updateStatus(orderDetail.getMaterialPurchaseRequirementId());
        }
        purchaseMaterialRequirementService.batchUpdateStatus(requirementIdList);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveBatchPurchaseOrderRecord(SaveBatchPurchaseOrderRecordDto saveBatchPurchaseOrderRecordDto, Integer status) {
        Long receiptsId = null;
        PurchaseOrder purchaseOrderParam = saveBatchPurchaseOrderRecordDto.getPurchaseOrder();
        if (purchaseOrderParam != null) {
            receiptsId = purchaseOrderParam.getId();
        }
        logger.info("批量保存采购订单的receipts_id：{}，入参：{}", receiptsId, JsonUtils.toString(saveBatchPurchaseOrderRecordDto));
        //校验采购需求是否已冻结
        List<PurchaseOrderReceiptsDto> purchaseOrderReceiptsDtoList = saveBatchPurchaseOrderRecordDto.getPurchaseOrderReceiptsDtoList();
        freezeCheck(purchaseOrderReceiptsDtoList);

        fillErpBuyerId(saveBatchPurchaseOrderRecordDto.getPurchaseOrderReceiptsDtoList());
        Long id = Long.valueOf(GenerateIDUtils.generateSixteenBitID());
        List<PurchaseOrderReceiptsDto> purchaseOrderRecordDto = saveBatchPurchaseOrderRecordDto.getPurchaseOrderReceiptsDtoList();
        //校验供应商是否是不可采购状态
        checkVendorPurchasingSiteFlag(purchaseOrderRecordDto);

        //赋最新的budgetOccupiedAmount值
        Map<String, BigDecimal> receiptsBudgetMap = new HashMap<>();
        List<Long> projectWbsReceiptsIdList = new ArrayList<>();
        List<String> wbsSummaryCodeList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(purchaseOrderRecordDto)) {
            for (PurchaseOrderReceiptsDto receiptsDto : purchaseOrderRecordDto) {
                if (CollectionUtils.isNotEmpty(receiptsDto.getPurchaseOrderDetailDtoList())) {
                    for (PurchaseOrderDetailDto purchaseOrderDetailDto : receiptsDto.getPurchaseOrderDetailDtoList()) {
                        projectWbsReceiptsIdList.add(purchaseOrderDetailDto.getProjectWbsReceiptsId());
                        wbsSummaryCodeList.add(purchaseOrderDetailDto.getWbsSummaryCode());
                    }
                }
            }
        }
        if (CollectionUtils.isNotEmpty(projectWbsReceiptsIdList)) {
            ProjectWbsReceiptsBudgetExample budgetExample = new ProjectWbsReceiptsBudgetExample();
            budgetExample.createCriteria().andDemandTypeEqualTo(ProjectWbsReceiptsBudgetDemandTypeEnums.DEMAND.getCode())
                    .andProjectWbsReceiptsIdIn(projectWbsReceiptsIdList).andWbsSummaryCodeIn(wbsSummaryCodeList).andDeletedFlagEqualTo(false);
            List<ProjectWbsReceiptsBudget> receiptsBudgetList = projectWbsReceiptsBudgetMapper.selectByExample(budgetExample);
            receiptsBudgetMap.putAll(receiptsBudgetList.stream().collect(Collectors.toMap(e -> e.getProjectWbsReceiptsId() + "_" + e.getWbsSummaryCode(),
                    e -> Optional.ofNullable(e.getBudgetOccupiedAmount()).orElse(BigDecimal.ZERO))));
        }

        if (CollectionUtils.isNotEmpty(purchaseOrderRecordDto)) {
            for (PurchaseOrderReceiptsDto receiptsDto : purchaseOrderRecordDto) {
                if (CollectionUtils.isNotEmpty(receiptsDto.getPurchaseOrderDetailDtoList())) {
                    for (PurchaseOrderDetailDto purchaseOrderDetailDto : receiptsDto.getPurchaseOrderDetailDtoList()) {
                        if (receiptsBudgetMap.containsKey(purchaseOrderDetailDto.getProjectWbsReceiptsId() + "_" + purchaseOrderDetailDto.getWbsSummaryCode())) {
                            purchaseOrderDetailDto.setBudgetOccupiedAmount(receiptsBudgetMap.get(purchaseOrderDetailDto.getProjectWbsReceiptsId() + "_" + purchaseOrderDetailDto.getWbsSummaryCode()));
                        }
                    }
                }
            }
        }
        saveBatchPurchaseOrderRecordDto.setPurchaseOrderReceiptsDtoList(purchaseOrderRecordDto);

        if (receiptsId != null) {
            //根据单据号修改;
            id = this.newReceiptsIdUpdate(saveBatchPurchaseOrderRecordDto, status);
        } else {
            this.newSave(purchaseOrderRecordDto, id, status);
        }
        logger.info("生成或查询的id为:{}", id);
        purchaseOrderCallBackService.updateRequirementStatusByOrderId(Long.toString(id));
        return id;
    }

    private void freezeCheck(List<PurchaseOrderReceiptsDto> purchaseOrderReceiptsDtoList) {
        List<String> freezeErrorMessages = new ArrayList<>();
        for (PurchaseOrderReceiptsDto purchaseOrderReceiptsDto : purchaseOrderReceiptsDtoList) {
            List<PurchaseOrderDetailDto> purchaseOrderDetailDtoList = purchaseOrderReceiptsDto.getPurchaseOrderDetailDtoList();
            for (PurchaseOrderDetailDto purchaseOrderDetailDto : purchaseOrderDetailDtoList) {
                Long id = purchaseOrderDetailDto.getRequirementId();
                PurchaseMaterialRequirement requirement = purchaseMaterialRequirementMapper.selectByPrimaryKey(id);
                if (requirement != null) {
                    if (requirement.getFreezeFlag().equals(FreezeFlag.FREEZE.getCode())) {
                        freezeErrorMessages.add(String.format("[%s]的[%s]冻结状态为已冻结,不可进行下达", requirement.getRequirementCode(), requirement.getPamCode()));
                    }
                }
            }
        }
        if (!freezeErrorMessages.isEmpty()) {
            throw new BizException(Code.ERROR, String.join(";", freezeErrorMessages));
        }
    }

    private void checkVendorPurchasingSiteFlag(List<PurchaseOrderReceiptsDto> purchaseOrderRecordList) {
        if (CollectionUtils.isEmpty(purchaseOrderRecordList)) {
            return;
        }
        for (PurchaseOrderReceiptsDto purchaseOrderReceiptsDto : purchaseOrderRecordList) {
            List<PurchaseOrderDetailDto> purchaseOrderDetailDtoList = purchaseOrderReceiptsDto.getPurchaseOrderDetailDtoList();
            if (CollectionUtils.isEmpty(purchaseOrderDetailDtoList)) {
                continue;
            }
            String vendorId = purchaseOrderDetailDtoList.get(0).getVendorId();
            if (StringUtils.isNotEmpty(vendorId)) {
                VendorSiteBankDto vendorSiteBankDto = basedataExtService.getVendorSiteBankDto(Long.valueOf(vendorId));
                // 校验供应商是否是不可采购状态
                if (!Objects.equals(vendorSiteBankDto.getPurchasingSiteFlag(), "Y")) {
                    throw new BizException(Code.ERROR, String.format("%s当前是不可采购状态，如需采购请至SRM系统进行修改", vendorSiteBankDto.getVendorName()));
                }
            }
        }
    }

    private void fillErpBuyerId(List<PurchaseOrderReceiptsDto> purchaseOrderReceiptsDtoList) {
        for (PurchaseOrderReceiptsDto por : purchaseOrderReceiptsDtoList) {
            PurchaseOrderTitleDto title = por.getPurchaseOrderTitle();
            List<SdpBuyersDto> buyers = buyersService.getBuyer(title.getProjectOuId());
            if (buyers.isEmpty()) {
                throw new BizException(Code.ERROR, "未维护有效的ERP采购员");
            } else if (buyers.size() > 1) {
                throw new BizException(Code.ERROR, "员工编码存在多条记录");
            }
            title.setErpBuyerId(buyers.get(0).getErpAgentId());
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long newReceiptsIdUpdate(SaveBatchPurchaseOrderRecordDto saveBatchPurchaseOrderRecordDto, Integer status) {
        logger.info("newReceiptsIdUpdate的saveBatchPurchaseOrderRecordDto：{}，status：{}", JsonUtils.toString(saveBatchPurchaseOrderRecordDto), status);
        PurchaseOrder purchaseOrderParam = saveBatchPurchaseOrderRecordDto.getPurchaseOrder();
        Guard.notNull(purchaseOrderParam, "采购订单头信息不能为空");

        PurchaseOrder oldPurchaseOrder = null;
        if (purchaseOrderParam != null && purchaseOrderParam.getId() != null) {
            //判断是否审批中了
            PurchaseOrderExample purchaseOrderExample = new PurchaseOrderExample();
            purchaseOrderExample.createCriteria().andReceiptsIdEqualTo(purchaseOrderParam.getId());
            List<PurchaseOrder> purchaseOrderList = purchaseOrderMapper.selectByExample(purchaseOrderExample);
            if (CollectionUtils.isNotEmpty(purchaseOrderList)) {
                oldPurchaseOrder = purchaseOrderList.get(0);

                if (Objects.equals(PurchaseOrderStatus.PENDING.code(), oldPurchaseOrder.getOrderStatus())) {
                    throw new ApplicationBizException("不能重复提交审批");
                }
            }
        }

        //单据id
        Long receiptsId = purchaseOrderParam.getId();

        // 根据单据号删除采购订单历史暂存数据
        purchaseOrderMergeExtMapper.deletebyReceiptsId(receiptsId);
//        purchaseOrderExtMapper.deletebyReceiptsId(receiptsId);
        purchaseOrderDetailExtMapper.deletebyReceiptsId(receiptsId);

        List<PurchaseOrderReceiptsDto> purchaseOrderRecordDto = saveBatchPurchaseOrderRecordDto.getPurchaseOrderReceiptsDtoList();
        if (CollectionUtils.isEmpty(purchaseOrderRecordDto)) {
            return receiptsId;
        }
        for (PurchaseOrderReceiptsDto dto : purchaseOrderRecordDto) {
            List<PurchaseOrderDetailDto> dtoPurchaseOrderDetailDtoList = dto.getPurchaseOrderDetailDtoList();
            List<CtcAttachmentDto> ctcAttachmentList = dto.getCtcAttachmentList();

            // 更新头
            PurchaseOrderTitleDto purchaseOrderTitleDto = dto.getPurchaseOrderTitle();
            List<StandardTermsDto> standardTermsDtoList = purchaseOrderTitleDto.getStandardTermsDtoList();
            PurchaseOrder purchaseOrder = new PurchaseOrder();
            BeanUtils.copyProperties(purchaseOrderTitleDto, purchaseOrder);
            if (purchaseOrder.getId() == null) {
                if (oldPurchaseOrder != null) {
                    purchaseOrder.setId(oldPurchaseOrder.getId());
                } else {
                    logger.error("批量保存采购订单头id丢失，receipts_id：{}，采购订单信息：{}，入参：{}",
                            receiptsId, JsonUtils.toString(purchaseOrderTitleDto), JsonUtils.toString(saveBatchPurchaseOrderRecordDto));
                    throw new ApplicationBizException("采购订单头信息不完整，请联系系统管理员");
                }
            }
            purchaseOrder.setCurrency(purchaseOrderTitleDto.getCurrencyCode()); //币种

            PurchaseOrder po = purchaseOrderMapper.selectByPrimaryKey(purchaseOrder.getId());
            Guard.notNull(po, String.format("采购订单id：%s不存在", purchaseOrder.getId()));
            if (StringUtils.isNotEmpty(po.getNum())) {
                purchaseOrder.setNum(null);
            } else if (StringUtils.isEmpty(purchaseOrder.getNum())) {
                purchaseOrder.setNum(null);
            }

            purchaseOrderMapper.updateByPrimaryKeySelective(purchaseOrder);

            // 删除附件
            CtcAttachmentDto ctcAttachmentDto = new CtcAttachmentDto();
            ctcAttachmentDto.setModule(CtcAttachmentModule.PURCHASE_ORDER.code());
            ctcAttachmentDto.setModuleId(purchaseOrder.getId());
            ctcAttachmentService.deleteByModule(ctcAttachmentDto);
            // 保存文件 start
            for (CtcAttachmentDto attachmentDto : ctcAttachmentList) {
                attachmentDto.setModuleId(purchaseOrder.getId());
                attachmentDto.setModule(CtcAttachmentModule.PURCHASE_ORDER.code());
                attachmentDto.setDeletedFlag(Optional.ofNullable(attachmentDto.getDeletedFlag()).orElse(DeletedFlag.VALID.code()));
                attachmentDto.setId(null);
                ctcAttachmentMapper.insertSelective(attachmentDto);
            }
            // 保存文件 end
            for (int j = 0; j < dtoPurchaseOrderDetailDtoList.size(); j++) {
                PurchaseOrderDetailDto purchaseOrderDetailDto = dtoPurchaseOrderDetailDtoList.get(j);

                List<PurchaseOrderDetailDto> purchaseOrderDetailDtoList = purchaseOrderDetailDto.getChildren();
                //保存订单行数据 start
                //需求id过来,保存到对应字段;
                purchaseOrderDetailDto.setId(null);
                purchaseOrderDetailDto.setDeletedFlag(Boolean.FALSE);
                purchaseOrderDetailDto.setPurchaseOrderId(purchaseOrder.getId());
                purchaseOrderDetailDto.setReceiptsId(receiptsId);
                if (!StringUtils.isEmpty(purchaseOrderDetailDto.getVendorId())) {
                    purchaseOrderDetailDto.setVendorAslId(Long.valueOf(purchaseOrderDetailDto.getVendorId()));
                } else if (!StringUtils.isEmpty(purchaseOrderDetailDto.getVendorAslId())) {
                    purchaseOrderDetailDto.setVendorAslId(purchaseOrderDetailDto.getVendorAslId());
                }
                purchaseOrderDetailDto.setVendorNum(Optional.ofNullable(purchaseOrderDetailDto.getVendorNum()).orElse(purchaseOrderDetailDto.getVendorCode()));
                purchaseOrderDetailDto.setCancelNum(new BigDecimal(0));
                purchaseOrderDetailDto.setProjectId(purchaseOrderDetailDto.getProjectId());
                purchaseOrderDetailDto.setStatus(PurchaseOrderDetailStatus.ORDER_RELEASED.code());
                purchaseOrderDetailDto.setLineNumber(j + 1);
                Long materialPurchaseRequirementId = purchaseOrderDetailDto.getId();
                if (purchaseOrderDetailDto.getRequirementId() != null) {
                    materialPurchaseRequirementId = purchaseOrderDetailDto.getRequirementId();
                }
                purchaseOrderDetailDto.setMaterialPurchaseRequirementId(materialPurchaseRequirementId);
                if (ListUtils.isNotEmpty(purchaseOrderDetailDtoList)) {
                    purchaseOrderDetailDto.setMergeRows(true);
                } else {
                    purchaseOrderDetailDto.setMergeRows(false);
                    // 校验采购需求未下达量是否超
                    checkRequirementNum(purchaseOrderDetailDto.getErpCode(), materialPurchaseRequirementId, purchaseOrderDetailDto.getOrderNum());
                }
                purchaseOrderDetailMapper.insertSelective(purchaseOrderDetailDto);
                //保存订单行数据 end
                //合并表冗余start
                if (purchaseOrderDetailDtoList == null || purchaseOrderDetailDtoList.size() == 0) {
                    PurchaseOrderMerge purchaseOrderMergeRedundant = new PurchaseOrderMerge();
                    BeanUtils.copyProperties(purchaseOrderDetailDto, purchaseOrderMergeRedundant);
                    if (materialPurchaseRequirementId != null) {
                        purchaseOrderMergeRedundant.setMaterialPurchaseRequirementId(materialPurchaseRequirementId);
                    }
                    purchaseOrderMergeRedundant.setId(null);
                    purchaseOrderMergeRedundant.setDeletedFlag(Boolean.FALSE);
                    purchaseOrderMergeRedundant.setPurchaseOrderId(purchaseOrderDetailDto.getId());
                    purchaseOrderMergeRedundant.setReceiptsId(receiptsId);
                    purchaseOrderMergeRedundant.setCancelNum(new BigDecimal(0));
                    purchaseOrderMergeRedundant.setMergeRows(0);
                    purchaseOrderMergeMapper.insertSelective(purchaseOrderMergeRedundant);
                }
                // end

                //保存合并数据
                for (int k = 0; purchaseOrderDetailDtoList != null && k < purchaseOrderDetailDtoList.size(); k++) {
                    PurchaseOrderMerge purchaseOrderMerge = new PurchaseOrderMerge();
                    BeanUtils.copyProperties(purchaseOrderDetailDtoList.get(k), purchaseOrderMerge);
                    if (purchaseOrderDetailDtoList.get(k).getMaterialPurchaseRequirementId() == null) {
                        purchaseOrderMerge.setMaterialPurchaseRequirementId(purchaseOrderDetailDtoList.get(k).getId());
                    }
                    purchaseOrderMerge.setId(null);
                    purchaseOrderMerge.setDeletedFlag(Boolean.FALSE);
                    purchaseOrderMerge.setPurchaseOrderId(purchaseOrderDetailDto.getId());
                    purchaseOrderMerge.setReceiptsId(receiptsId);
                    purchaseOrderMerge.setCancelNum(new BigDecimal(0));
                    purchaseOrderMerge.setMergeRows(1);
                    // 校验采购需求未下达量是否超(如果是暂存操作，排除掉自己的占用)
                    checkRequirementNum(purchaseOrderMerge.getErpCode(),
                            purchaseOrderMerge.getMaterialPurchaseRequirementId(), purchaseOrderMerge.getOrderNum());
                    purchaseOrderMergeMapper.insertSelective(purchaseOrderMerge);
                }
            }

            //先删除标准条款信息，再新增
            if(ContractTermsFlgEnum.STANDARD_TERMS.getCode().equals(purchaseOrderTitleDto.getContractTermsFlg())){
                Long purchaseOrderId = purchaseOrderTitleDto.getId();
                PurchaseOrderStandardTermsExample purchaseOrderStandardTermsExample = new PurchaseOrderStandardTermsExample();
                PurchaseOrderStandardTermsExample.Criteria criteria = purchaseOrderStandardTermsExample.createCriteria();
                criteria.andDeletedFlagEqualTo(false).andPurchaseOrderIdEqualTo(purchaseOrderId);
                List<PurchaseOrderStandardTerms> purchaseOrderStandardTermsList = purchaseOrderStandardTermsMapper.selectByExample(purchaseOrderStandardTermsExample);
                if (CollectionUtils.isNotEmpty(purchaseOrderStandardTermsList)){
                    for (PurchaseOrderStandardTerms purchaseOrderStandardTerms : purchaseOrderStandardTermsList) {
                        Long id = purchaseOrderStandardTerms.getId();
                        purchaseOrderStandardTerms.setDeletedFlag(true);
                        purchaseOrderStandardTermsMapper.updateByPrimaryKey(purchaseOrderStandardTerms);
                        //删除内容信息
                        PurchaseOrderStandardTermsContentExample purchaseOrderStandardTermsContentExample = new PurchaseOrderStandardTermsContentExample();
                        PurchaseOrderStandardTermsContentExample.Criteria purchaseOrderStandardTermsContentExampleCriteria = purchaseOrderStandardTermsContentExample.createCriteria();
                        purchaseOrderStandardTermsContentExampleCriteria.andDeletedFlagEqualTo(false).andAssociationPurchaseTermsIdEqualTo(id);
                        List<PurchaseOrderStandardTermsContent> purchaseOrderStandardTermsContents = purchaseOrderStandardTermsContentMapper.selectByExample(purchaseOrderStandardTermsContentExample);
                        if (CollectionUtils.isNotEmpty(purchaseOrderStandardTermsContents)){
                            for (PurchaseOrderStandardTermsContent purchaseOrderStandardTermsContent : purchaseOrderStandardTermsContents) {
                                purchaseOrderStandardTermsContent.setDeletedFlag(true);
                                purchaseOrderStandardTermsContentMapper.updateByPrimaryKey(purchaseOrderStandardTermsContent);
                            }
                        }
                        //删除偏离项信息 standard_terms_deviation
                        StandardTermsDeviationExample standardTermsDeviationExample = new StandardTermsDeviationExample();
                        StandardTermsDeviationExample.Criteria standardTermsDeviationExampleCriteria = standardTermsDeviationExample.createCriteria();
                        standardTermsDeviationExampleCriteria.andDeletedFlagEqualTo(false).andAssociationPurchaseTermsIdEqualTo(id);
                        List<StandardTermsDeviation> standardTermsDeviations = standardTermsDeviationMapper.selectByExample(standardTermsDeviationExample);
                        if (CollectionUtils.isNotEmpty(standardTermsDeviations)){
                            for (StandardTermsDeviation standardTermsDeviation : standardTermsDeviations) {
                                standardTermsDeviation.setDeletedFlag(true);
                                standardTermsDeviationMapper.updateByPrimaryKey(standardTermsDeviation);
                            }
                        }
                    }
                }

                //新增标准条款信息
                standardTermsListSave(standardTermsDtoList,purchaseOrder);
            }


        }

        return receiptsId;
    }

    /**
     * 对比物料下达数量与采购需求未下达量
     *
     * @param erpCode
     * @param purchaseRequirementId
     * @param orderNum
     */
    @Override
    public void checkRequirementNum(String erpCode, Long purchaseRequirementId, BigDecimal orderNum) {
        if (purchaseRequirementId == null
                || orderNum == null
                || orderNum.compareTo(BigDecimal.ZERO) <= 0) {
            return;
        }
        BigDecimal unreleasedNum = purchaseMaterialRequirementExtMapper.queryUnreleasedNumByRequirementId(purchaseRequirementId, null);
        if (unreleasedNum == null) {
            unreleasedNum = BigDecimal.ZERO;
        }
        if (orderNum.compareTo(unreleasedNum) > 0) {
            throw new MipException(erpCode + "物料采购订单下达数量(" + orderNum + ") > 采购需求未下达量(" + unreleasedNum.setScale(2, RoundingMode.HALF_UP) + ")" +
                    "，不允许创建");
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void newSave(List<PurchaseOrderReceiptsDto> purchaseOrderRecordDto, Long id, Integer status) {
        for (int i = 0; i < purchaseOrderRecordDto.size(); i++) {
            PurchaseOrderReceiptsDto dto = purchaseOrderRecordDto.get(i);
            List<PurchaseOrderDetailDto> dtoPurchaseOrderDetailDtoList = dto.getPurchaseOrderDetailDtoList();
            List<CtcAttachmentDto> ctcAttachmentList = dto.getCtcAttachmentList();

            // 保存头 start
            PurchaseOrderTitleDto purchaseOrderTitleDto = dto.getPurchaseOrderTitle();
            List<StandardTermsDto> standardTermsDtoList = purchaseOrderTitleDto.getStandardTermsDtoList();
            PurchaseOrder purchaseOrder = new PurchaseOrder();
            BeanUtils.copyProperties(purchaseOrderTitleDto, purchaseOrder);
            purchaseOrder.setContractTerms(purchaseOrderTitleDto.getContractTermsFlg());
            purchaseOrder.setDeletedFlag(Boolean.FALSE);
            purchaseOrder.setReceiptsId(id);
            purchaseOrder.setId(null);
            purchaseOrder.setOrderStatus(status);
            if (!StringUtils.isEmpty(dtoPurchaseOrderDetailDtoList.get(0).getVendorId())) {
                purchaseOrder.setVendorAslId(Long.valueOf(dtoPurchaseOrderDetailDtoList.get(0).getVendorId()));
            } else if (Objects.nonNull(dtoPurchaseOrderDetailDtoList.get(0).getVendorAslId())) {
                purchaseOrder.setVendorAslId(dtoPurchaseOrderDetailDtoList.get(0).getVendorAslId());
            }
            purchaseOrder.setSyncStatus(PurchaseOrderSyncStatus.NOT_SYNCED.code());
            purchaseOrder.setSource("2");// 来源:非wbs:1;wbs:2
            if (Objects.nonNull(purchaseOrderTitleDto.getProjectOuId())) {
                purchaseOrder.setNum(generateOrderNum(purchaseOrderTitleDto.getProjectOuId()));
            }
            purchaseOrder.setProjectId(dtoPurchaseOrderDetailDtoList.get(0).getProjectId());
            purchaseOrder.setBuyerId(SystemContext.getUserId());
            UserInfo buyer = CacheDataUtils.findUserById(SystemContext.getUserId());
            purchaseOrder.setBuyerName(buyer.getName());
            purchaseOrder.setCurrency(purchaseOrderTitleDto.getCurrencyCode()); //币种
            purchaseOrder.setOuId(purchaseOrderTitleDto.getProjectOuId()); //业务实体
            purchaseOrder.setSyncSourceSystem("PAM");
            purchaseOrderMapper.insertSelective(purchaseOrder);
            // 保存头 end

            // 保存文件 start
            for (int j = 0; j < ctcAttachmentList.size(); j++) {
                ctcAttachmentList.get(j).setModuleId(purchaseOrder.getId());
                ctcAttachmentList.get(j).setModule(CtcAttachmentModule.PURCHASE_ORDER.code());
                ctcAttachmentList.get(j).setDeletedFlag(Optional.ofNullable(ctcAttachmentList.get(j).getDeletedFlag()).orElse(DeletedFlag.VALID.code()));
                ctcAttachmentList.get(j).setId(null);
                ctcAttachmentMapper.insertSelective(ctcAttachmentList.get(j));
            }
            // 保存文件 end
            for (int j = 0; j < dtoPurchaseOrderDetailDtoList.size(); j++) {
                PurchaseOrderDetailDto purchaseOrderDetailDto = dtoPurchaseOrderDetailDtoList.get(j);
                // 活动事项校验
                if (!StringUtils.isEmpty(purchaseOrderDetailDto.getActivityCode())
                        && !StringUtils.isEmpty(purchaseOrderDetailDto.getWbsSummaryCode())) {
                    // 先判断活动事项以及活动事项的上级（活动事项的上下级关系在“项目活动事项维护”中设置）存不存在，上级存在也算存在
                    String wbsBudgetCode = purchaseOrderDetailDto.getWbsSummaryCode();
                    // 截取wbscode,去除项目编号
                    wbsBudgetCode = wbsBudgetCode.substring(purchaseOrderDetailDto.getProjectNum().length() + 1, wbsBudgetCode.length());
                    ProjectWbsBudget projectWbsBudget =
                            projectWbsBudgetService.existWbsBudgetByActivity(purchaseOrderDetailDto.getProjectId(), wbsBudgetCode,
                                    purchaseOrderDetailDto.getActivityCode());
                    if (projectWbsBudget == null) {
                        // 查询配置了预算事项的活动事项或上级(判断这个活动事项是不是预算事项)
                        String budgetActivityCode =
                                projectWbsBudgetService.checkWbsBudgetByActivity(SystemContext.getUnitId(), purchaseOrderDetailDto.getActivityCode());
                        com.midea.mcomponent.core.util.Assert.isTrue(org.apache.commons.lang3.StringUtils.isNotBlank(budgetActivityCode),
                                "WBS[" + wbsBudgetCode + "]+活动事项[" + purchaseOrderDetailDto.getActivityCode() + "]的预算不存在，" +
                                        "且无法生成对应的项目预算（该活动事项及上级都为非预算事项）");
                        // 填报的WBS+活动事项（通过角色绑定）在这个项目的预算中不存在，则在预算中自动生成该条预算，预算金额为0
                        projectWbsBudgetService.addWbsBudgetByActivity(purchaseOrderDetailDto.getProjectId(), wbsBudgetCode, budgetActivityCode);
                    }
                }

                List<PurchaseOrderDetailDto> purchaseOrderDetailDtoList = purchaseOrderDetailDto.getChildren();
                //保存订单行数据 start
                //需求id过来,保存到对应字段;
                purchaseOrderDetailDto.setMaterialPurchaseRequirementId(purchaseOrderDetailDto.getId());
                purchaseOrderDetailDto.setId(null);
                purchaseOrderDetailDto.setDeletedFlag(Boolean.FALSE);
                purchaseOrderDetailDto.setPurchaseOrderId(purchaseOrder.getId());
                purchaseOrderDetailDto.setReceiptsId(id);
                if (!StringUtils.isEmpty(purchaseOrderDetailDto.getVendorId())) {
                    purchaseOrderDetailDto.setVendorAslId(Long.valueOf(purchaseOrderDetailDto.getVendorId()));
                } else if (!StringUtils.isEmpty(purchaseOrderDetailDto.getVendorAslId())) {
                    purchaseOrderDetailDto.setVendorAslId(purchaseOrderDetailDto.getVendorAslId());
                }
                purchaseOrderDetailDto.setVendorNum(Optional.ofNullable(purchaseOrderDetailDto.getVendorNum()).orElse(purchaseOrderDetailDto.getVendorCode()));
                purchaseOrderDetailDto.setCancelNum(new BigDecimal(0));
                purchaseOrderDetailDto.setProjectId(purchaseOrderDetailDto.getProjectId());
                purchaseOrderDetailDto.setStatus(PurchaseOrderDetailStatus.ORDER_RELEASED.code());
                purchaseOrderDetailDto.setLineNumber(j + 1);
                Long materialPurchaseRequirementId = purchaseOrderDetailDto.getId();
                if (purchaseOrderDetailDto.getRequirementId() != null) {
                    materialPurchaseRequirementId = purchaseOrderDetailDto.getRequirementId();
                }
                if (ListUtils.isNotEmpty(purchaseOrderDetailDtoList)) {
                    purchaseOrderDetailDto.setMergeRows(true);
                } else {
                    purchaseOrderDetailDto.setMergeRows(false);
                    // 校验采购需求未下达量是否超
                    checkRequirementNum(purchaseOrderDetailDto.getErpCode(), materialPurchaseRequirementId, purchaseOrderDetailDto.getOrderNum());
                }
                purchaseOrderDetailMapper.insertSelective(purchaseOrderDetailDto);
                //合并表冗余start
                if (purchaseOrderDetailDtoList == null || purchaseOrderDetailDtoList.size() == 0) {
                    PurchaseOrderMerge purchaseOrderMergeRedundant = new PurchaseOrderMerge();
                    BeanUtils.copyProperties(purchaseOrderDetailDto, purchaseOrderMergeRedundant);
                    if (materialPurchaseRequirementId != null) {
                        purchaseOrderMergeRedundant.setMaterialPurchaseRequirementId(materialPurchaseRequirementId);
                    }
                    purchaseOrderMergeRedundant.setId(null);
                    purchaseOrderMergeRedundant.setDeletedFlag(Boolean.FALSE);
                    purchaseOrderMergeRedundant.setPurchaseOrderId(purchaseOrderDetailDto.getId());
                    purchaseOrderMergeRedundant.setReceiptsId(id);
                    purchaseOrderMergeRedundant.setCancelNum(new BigDecimal(0));
                    purchaseOrderMergeRedundant.setMergeRows(0);
                    purchaseOrderMergeMapper.insertSelective(purchaseOrderMergeRedundant);
                }
                // end

                //保存订单行数据 end
                //保存合并数据
                for (int k = 0; purchaseOrderDetailDtoList != null && k < purchaseOrderDetailDtoList.size(); k++) {
                    PurchaseOrderMerge purchaseOrderMerge = new PurchaseOrderMerge();
                    BeanUtils.copyProperties(purchaseOrderDetailDtoList.get(k), purchaseOrderMerge);
                    if (purchaseOrderDetailDtoList.get(k).getMaterialPurchaseRequirementId() == null) {
                        purchaseOrderMerge.setMaterialPurchaseRequirementId(purchaseOrderDetailDtoList.get(k).getId());
                        // 校验采购需求未下达量是否超
                        checkRequirementNum(purchaseOrderMerge.getErpCode(),
                                purchaseOrderMerge.getMaterialPurchaseRequirementId(), purchaseOrderMerge.getOrderNum());
                    }
                    purchaseOrderMerge.setId(null);
                    purchaseOrderMerge.setDeletedFlag(Boolean.FALSE);
                    purchaseOrderMerge.setPurchaseOrderId(purchaseOrderDetailDto.getId());
                    purchaseOrderMerge.setReceiptsId(id);
                    purchaseOrderMerge.setCancelNum(new BigDecimal(0));
                    purchaseOrderMerge.setMergeRows(1);
                    purchaseOrderMergeMapper.insertSelective(purchaseOrderMerge);
                }
            }
            //保存标准条款信息
            if (ContractTermsFlgEnum.STANDARD_TERMS.getCode().equals(purchaseOrderTitleDto.getContractTermsFlg())){
                if (CollectionUtils.isNotEmpty(standardTermsDtoList)){
                    standardTermsListSave(standardTermsDtoList, purchaseOrder);
                }
            }
            //保存文档信息
        }
    }

    private void standardTermsListSave(List<StandardTermsDto> standardTermsDtoList, PurchaseOrder purchaseOrder) {
        for (StandardTermsDto standardTermsDto : standardTermsDtoList) {
            PurchaseOrderStandardTerms purchaseOrderStandardTerms = new PurchaseOrderStandardTerms();
            purchaseOrderStandardTerms.setPurchaseOrderId(purchaseOrder.getId());
            purchaseOrderStandardTerms.setAssociationTermsId(standardTermsDto.getAssociationTermsId());
            purchaseOrderStandardTerms.setTermsCode(standardTermsDto.getTermsCode());
            purchaseOrderStandardTerms.setTermsName(standardTermsDto.getTermsName());
            purchaseOrderStandardTerms.setTermsDisplayContent(standardTermsDto.getTermsDisplayContent());
            purchaseOrderStandardTerms.setDeletedFlag(false);
            Long userId = SystemContext.getUserId();
            UserInfo userInfo = CacheDataUtils.findUserById(userId);
            purchaseOrderStandardTerms.setCreateByName(userInfo.getName());
            purchaseOrderStandardTermsMapper.insert(purchaseOrderStandardTerms);
            List<StandardTermsContentDto> standardTermsContentList = standardTermsDto.getStandardTermsContentList();
            //文本框内容
            if (CollectionUtils.isNotEmpty(standardTermsContentList)){
                for (StandardTermsContentDto standardTermsContentDto : standardTermsContentList) {
                    PurchaseOrderStandardTermsContent purchaseOrderStandardTermsContent = new PurchaseOrderStandardTermsContent();
                    purchaseOrderStandardTermsContent.setAssociationPurchaseTermsId(purchaseOrderStandardTerms.getId());
                    purchaseOrderStandardTermsContent.setTextBoxKey(standardTermsContentDto.getTextBoxKey());
                    purchaseOrderStandardTermsContent.setTextBoxTitle(standardTermsContentDto.getTextBoxTitle());
                    purchaseOrderStandardTermsContent.setDefaultValue(standardTermsContentDto.getDefaultValue());
                    purchaseOrderStandardTermsContent.setDeletedFlag(false);
                    purchaseOrderStandardTermsContentMapper.insert(purchaseOrderStandardTermsContent);
                }
            }
            //偏离信息
            List<StandardTermsDeviation> standardTermsDeviationList = standardTermsDto.getStandardTermsDeviationList();
            if (CollectionUtils.isNotEmpty(standardTermsDeviationList)){
                for (StandardTermsDeviation standardTermsDeviation : standardTermsDeviationList) {
                    StandardTermsDeviation deviation = new StandardTermsDeviation();
                    deviation.setAssociationPurchaseTermsId(purchaseOrderStandardTerms.getId());
                    deviation.setDeviationInfo(standardTermsDeviation.getDeviationInfo());
                    deviation.setDeletedFlag(false);
                    standardTermsDeviationMapper.insert(deviation);
                }
            }
        }
    }

    @Override
    public SaveBatchPurchaseOrderRecordDto getDetails(Long receiptsId, Integer editFlag) {
        String json = String.valueOf(redisUtilServer.get(RedisContant.SAVE_BATCH_PURCHASE_ORDER_RECORD_TS + receiptsId));
        SaveBatchPurchaseOrderRecordDto obj = JSON.parseObject(json, SaveBatchPurchaseOrderRecordDto.class);
        if (obj != null) {
            return obj;
        }
        SaveBatchPurchaseOrderRecordDto saveBatchPurchaseOrderRecordDto = new SaveBatchPurchaseOrderRecordDto();

        List<PurchaseOrderReceiptsDto> purchaseOrderRecordDto = new ArrayList<>();
        List<PurchaseOrderDetailDto> purchaseOrderDetailAllList = new ArrayList<>();

        PurchaseOrderExample purchaseOrderExample = new PurchaseOrderExample();
        purchaseOrderExample.createCriteria().andReceiptsIdEqualTo(receiptsId).andDeletedFlagEqualTo(Boolean.FALSE);
        List<PurchaseOrder> purchaseOrderList = purchaseOrderMapper.selectByExampleWithBLOBs(purchaseOrderExample);
        Map<Long, BigDecimal> conversionRateMap = purchaseOrderList.stream()
                .collect(Collectors.toMap(PurchaseOrder::getId, PurchaseOrder::getConversionRate));
        Map<Long, String> erpBuyerNames = new HashMap<>();
        for (int i = 0; i < purchaseOrderList.size(); i++) {
            PurchaseOrderReceiptsDto purchaseOrderReceiptsDto = new PurchaseOrderReceiptsDto();
            PurchaseOrderTitleDto purchaseOrderTitleDto = new PurchaseOrderTitleDto();
            PurchaseOrder purchaseOrder = purchaseOrderList.get(i);

            BeanUtils.copyProperties(purchaseOrder, purchaseOrderTitleDto);
            purchaseOrderTitleDto.setErpBuyerName(erpBuyerNames.computeIfAbsent(purchaseOrder.getErpBuyerId(), this::getErpBuyerName));
            purchaseOrderTitleDto.setProjectOuId(purchaseOrder.getOuId());
            purchaseOrderTitleDto.setCurrencyCode(purchaseOrder.getCurrency());
            // 获取标准条款信息
            if (ContractTermsFlgEnum.STANDARD_TERMS.getCode().equals(purchaseOrder.getContractTermsFlg())) {
                PurchaseOrderStandardTermsExample purchaseOrderStandardTermsExample = new PurchaseOrderStandardTermsExample();
                PurchaseOrderStandardTermsExample.Criteria termsExampleCriteria = purchaseOrderStandardTermsExample.createCriteria();
                termsExampleCriteria.andDeletedFlagEqualTo(false).andPurchaseOrderIdEqualTo(purchaseOrder.getId());
                List<PurchaseOrderStandardTerms> purchaseOrderStandardTerms = purchaseOrderStandardTermsMapper.selectByExampleWithBLOBs(purchaseOrderStandardTermsExample);
                if (CollectionUtils.isNotEmpty(purchaseOrderStandardTerms)) {
                    List<PurchaseOrderStandardTermsDto> purchaseOrderStandardTermsDtoList = new ArrayList<>();
                    purchaseOrderStandardTermsDtoListSave(purchaseOrderStandardTerms,purchaseOrderStandardTermsDtoList);
                    List<StandardTermsDto> standardTermsDtoList = new ArrayList<>();
                    for (PurchaseOrderStandardTermsDto purchaseOrderStandardTermsDto : purchaseOrderStandardTermsDtoList) {
                        StandardTermsDto standardTermsDto = new StandardTermsDto();
                        standardTermsDto.setId(purchaseOrderStandardTermsDto.getId());
                        standardTermsDto.setPurchaseOrderId(purchaseOrderStandardTermsDto.getPurchaseOrderId());
                        standardTermsDto.setAssociationTermsId(purchaseOrderStandardTermsDto.getAssociationTermsId());
                        standardTermsDto.setTermsCode(purchaseOrderStandardTermsDto.getTermsCode());
                        standardTermsDto.setTermsName(purchaseOrderStandardTermsDto.getTermsName());
                        standardTermsDto.setRemark(purchaseOrderStandardTermsDto.getRemark());
                        standardTermsDto.setTermsDisplayContent(purchaseOrderStandardTermsDto.getTermsDisplayContent());

                        standardTermsDto.setStandardTermsDeviationList(purchaseOrderStandardTermsDto.getStandardTermsDeviationList());
                        standardTermsDtoList.add(standardTermsDto);
                    }
                    purchaseOrderTitleDto.setStandardTermsDtoList(standardTermsDtoList);
                }
            }
            purchaseOrderReceiptsDto.setPurchaseOrderTitle(purchaseOrderTitleDto);

            PurchaseOrderDto purchaseOrderDto = new PurchaseOrderDto();
            BeanUtils.copyProperties(purchaseOrder, purchaseOrderDto);
            purchaseOrderDto.setErpBuyerName(erpBuyerNames.get(purchaseOrder.getErpBuyerId()));
            purchaseOrderDto.setProjectOuId(purchaseOrder.getOuId());
            purchaseOrderDto.setCurrencyCode(purchaseOrder.getCurrency());
            purchaseOrderReceiptsDto.setPurchaseOrderDto(purchaseOrderDto);
            // 是否满足更新一揽子的条件
            boolean condition = isUpdatePrice(purchaseOrderDto.getOrderStatus(), purchaseOrderDto.getPricingType());
            if (condition) {
                updateOrderPrice(purchaseOrderDto);
            }
            // get合并数据  start
            PurchaseOrderDetailExample purchaseOrderReceiptsExample = new PurchaseOrderDetailExample();
            PurchaseOrderDetailExample.Criteria criteriaReceipts = purchaseOrderReceiptsExample.createCriteria();
            criteriaReceipts.andPurchaseOrderIdEqualTo(purchaseOrderList.get(i).getId())
                    .andRecordIdIsNull()
                    .andDeletedFlagEqualTo(Boolean.FALSE);
            List<PurchaseOrderDetail> purchaseOrderDetailList = purchaseOrderDetailMapper.selectByExample(purchaseOrderReceiptsExample);

            //批量查询 需求预算占用金额(汇总)
            List<Long> projectWbsReceiptsIdList = purchaseOrderDetailList.stream().map(PurchaseOrderDetail::getProjectWbsReceiptsId).filter(Objects::nonNull).collect(Collectors.toList());
            projectWbsReceiptsIdList.add(-1L); //防空
            ProjectWbsReceiptsBudgetExample receiptsBudgetExample = new ProjectWbsReceiptsBudgetExample();
            receiptsBudgetExample.createCriteria().andProjectWbsReceiptsIdIn(projectWbsReceiptsIdList)
                    .andDemandTypeEqualTo(ProjectWbsReceiptsBudgetDemandTypeEnums.ALL.getCode())
                    .andDeletedFlagEqualTo(Boolean.FALSE);
            List<ProjectWbsReceiptsBudget> projectWbsReceiptsBudgetList = projectWbsReceiptsBudgetMapper.selectByExample(receiptsBudgetExample);
            Map<String, BigDecimal> budgetOccupiedAmountTotalMap = projectWbsReceiptsBudgetList.stream().collect(Collectors.toMap(s -> buildProjectWbsReceiptsBudgetKey(s.getWbsSummaryCode(), s.getProjectWbsReceiptsId()), s -> s.getBudgetOccupiedAmount(), (s1, s2) -> s1));

            List<PurchaseOrderDetailDto> purchaseOrderDetailDtoArrayList = new ArrayList<>();
            for (int k = 0; k < purchaseOrderDetailList.size(); k++) {
                PurchaseOrderDetailDto purchaseOrderDetailDto = new PurchaseOrderDetailDto();
                BeanUtils.copyProperties(purchaseOrderDetailList.get(k), purchaseOrderDetailDto);
                PurchaseOrderMergeExample purchaseOrderMergeExample = new PurchaseOrderMergeExample();
                purchaseOrderMergeExample.createCriteria()
                        .andPurchaseOrderIdEqualTo(purchaseOrderDetailDto.getId())
                        .andReceiptsIdEqualTo(receiptsId)
                        .andDeletedFlagEqualTo(Boolean.FALSE)
                        .andMergeRowsEqualTo(1);
                List<PurchaseOrderMerge> purchaseOrderMergeList = purchaseOrderMergeMapper.selectByExample(purchaseOrderMergeExample);

                //需求预算占用金额(汇总)
                String key = buildProjectWbsReceiptsBudgetKey(purchaseOrderDetailDto.getWbsSummaryCode(), purchaseOrderDetailDto.getProjectWbsReceiptsId());
                purchaseOrderDetailDto.setBudgetOccupiedAmountTotal(budgetOccupiedAmountTotalMap.getOrDefault(key, BigDecimal.ZERO));

                List<PurchaseOrderDetailDto> purchaseOrderDetailNewList = new ArrayList<>();
                for (int n = 0; n < purchaseOrderMergeList.size(); n++) {
                    PurchaseOrderDetailDto purchaseOrderDetail = new PurchaseOrderDetailDto();
                    BeanUtils.copyProperties(purchaseOrderMergeList.get(n), purchaseOrderDetail);
                    // purchase_order_merge 没有 projectWbsReceiptsId
                    purchaseOrderDetail.setProjectWbsReceiptsId(purchaseOrderDetailDto.getProjectWbsReceiptsId());
                    purchaseOrderDetail.setRequirementId(purchaseOrderDetail.getMaterialPurchaseRequirementId());
                    purchaseOrderDetailNewList.add(purchaseOrderDetail);
                }
                purchaseOrderDetailDto.setChildren(purchaseOrderDetailNewList);
                purchaseOrderDetailDto.setRequirementId(purchaseOrderDetailDto.getMaterialPurchaseRequirementId());
                purchaseOrderDetailDtoArrayList.add(purchaseOrderDetailDto);
            }
            // 根据每个采购订单行/合并行的
            setPurchaseOrderDetailRemainMoney(purchaseOrderDetailDtoArrayList);
            purchaseOrderReceiptsDto.setPurchaseOrderDetailDtoList(purchaseOrderDetailDtoArrayList);
            purchaseOrderDetailAllList.addAll(purchaseOrderDetailDtoArrayList);
            // get合并数据  end

            // get文件数据  start
            CtcAttachmentExample ctcAttachmentExample = new CtcAttachmentExample();
            CtcAttachmentExample.Criteria criteria1 = ctcAttachmentExample.createCriteria();
            criteria1.andModuleEqualTo(CtcAttachmentModule.PURCHASE_ORDER.code());
            criteria1.andModuleIdEqualTo(purchaseOrderList.get(i).getId());
            criteria1.andDeletedFlagEqualTo(DeletedFlag.VALID.code());
            List<CtcAttachment> ctcAttachmentList = ctcAttachmentMapper.selectByExample(ctcAttachmentExample);
            List<CtcAttachmentDto> ctcAttachmentDtoList = new ArrayList<>();
            for (int j = 0; j < ctcAttachmentList.size(); j++) {
                CtcAttachmentDto ctcAttachmentDto = new CtcAttachmentDto();
                BeanUtils.copyProperties(ctcAttachmentList.get(j), ctcAttachmentDto);
                UserInfo userInfo = CacheDataUtils.findUserById(ctcAttachmentList.get(j).getCreateBy());
                if (userInfo != null) {
                    ctcAttachmentDto.setCreateUserName(userInfo.getName());
                }
                ctcAttachmentDtoList.add(ctcAttachmentDto);
            }
            purchaseOrderReceiptsDto.setCtcAttachmentList(ctcAttachmentDtoList);
            // get文件数据  end

            purchaseOrderRecordDto.add(purchaseOrderReceiptsDto);
        }

        //如果是重新编辑，实时查历史价
        if (Objects.equals(editFlag, 1)) {
            //批量查询订单行库存组织ID
            List<Long> materielIdList = purchaseOrderDetailAllList.stream().map(PurchaseOrderDetail::getMaterielId).collect(Collectors.toList());
            Map<Long, Long> organizationMap = basedataExtService.getMaterialByIds(materielIdList);
            purchaseOrderDetailAllList.forEach(s -> s.setOrgId(organizationMap.get(s.getMaterielId())));

            //批量查询物料历史价格
            List<Long> orgErrorList = new ArrayList<>();
           // Map<String, BigDecimal> materialPriceMap = this.getMaterialPriceMap(purchaseOrderDetailAllList, orgErrorList);
            Map<String, MaterialPrice> materialPriceEntityMap = this.getMaterialPriceEntityMap(purchaseOrderDetailAllList, orgErrorList);
            for (PurchaseOrderDetailDto detailDto : purchaseOrderDetailAllList) {
                if (!orgErrorList.contains(detailDto.getOrgId())) {
                    //用物料价格表取到的“最新价格”/订单上的汇率，作为该物料的历史价
                    BigDecimal conversionRate = conversionRateMap.getOrDefault(detailDto.getPurchaseOrderId(), BigDecimal.ONE);
                   // BigDecimal amount = materialPriceMap.get(buildMaterialPriceGroupKey(detailDto.getErpCode(), detailDto.getOrgId()));
                    MaterialPrice materialPrice = materialPriceEntityMap.get(buildMaterialPriceGroupKey(detailDto.getErpCode(), detailDto.getOrgId()));
                    BigDecimal amount = materialPrice.getAmount();
                    detailDto.setStandardHistoryPrice(amount);
                    detailDto.setHistoryPriceOrderDetailId(materialPrice.getPurchaseBpaPriceId());
                    detailDto.setHistoryPrice(null);
                    if (amount != null) {
                        detailDto.setHistoryPrice(BigDecimalUtils.divide(amount, conversionRate));
                    } else {
                        detailDto.setErrorMsg("历史价不存在");
                    }
                } else {
                    detailDto.setErrorMsg("组织参数：WBS采购订单历史价价格类型，配置有误，请联系IT处理");
                }
            }
        }

        for (PurchaseOrderDetailDto purchaseOrderDetailDto : purchaseOrderDetailAllList) {
            Long historyPriceOrderDetailId = purchaseOrderDetailDto.getHistoryPriceOrderDetailId();
            if(Objects.nonNull(historyPriceOrderDetailId)){
                PurchaseOrderDetail historyOrderDetail = purchaseOrderDetailMapper.selectByPrimaryKey(historyPriceOrderDetailId);
                if(Objects.nonNull(historyOrderDetail)){
                    Long historyOrderDetailProjectId = historyOrderDetail.getProjectId();
                    Project historyProject = projectService.selectByPrimaryKey(historyOrderDetailProjectId);
                    purchaseOrderDetailDto.setHistoryPriceProjectCode(historyProject.getCode());
                    purchaseOrderDetailDto.setHistoryPriceProjectName(historyProject.getName());
                }
            }
        }

        saveBatchPurchaseOrderRecordDto.setPurchaseOrderReceiptsDtoList(purchaseOrderRecordDto);

        //saveBatchPurchaseOrderRecordDto.setPurchaseOrder(purchaseOrder);
        return saveBatchPurchaseOrderRecordDto;
    }

    @Override
    public List<AssociateOccupiedBudgetDetailsVo> associateOccupiedBudgetDetails(Long projectWbsReceiptsId,
                                                                                 String wbsSummaryCode,
                                                                                 String num) {
        List<AssociateOccupiedBudgetDetailsVo> assList = new ArrayList<>();
        List<PurchaseOrderDetailDto> discountMoneyList = purchaseOrderExtMapper.getDiscountMoneyByProjectWbsReceiptsIdList(
                Arrays.asList(projectWbsReceiptsId), Arrays.asList(wbsSummaryCode), num);
        if (ListUtils.isEmpty(discountMoneyList)) {
            return assList;
        }
        // 根据采购订单头汇总
        Map<Long, List<PurchaseOrderDetailDto>> orderDetailMap =
                discountMoneyList.stream().collect(Collectors.groupingBy(PurchaseOrderDetailDto::getId));
        orderDetailMap.entrySet().forEach(entry -> {
            List<PurchaseOrderDetailDto> orderDetailDtoList = entry.getValue();
            PurchaseOrderDetailDto orderDetail = orderDetailDtoList.get(0);
            BigDecimal discountMoney = orderDetailDtoList.stream().map(e ->
                    Optional.ofNullable(e.getDiscountMoney()).orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO, BigDecimal::add);
            AssociateOccupiedBudgetDetailsVo assVo = new AssociateOccupiedBudgetDetailsVo();
            assVo.setId(orderDetail.getId());
            assVo.setOrderNo(orderDetail.getNum());
            assVo.setStatus(orderDetail.getOrderStatus());
            discountMoney = discountMoney.multiply(StringUtils.isEmpty(orderDetail.getConversionRate()) ? BigDecimal.ZERO
                    : new BigDecimal(orderDetail.getConversionRate()));
            assVo.setDiscountMoney(discountMoney);
            assList.add(assVo);
        });
        return assList;
    }

    @Override
    public List<AssociateOccupiedBudgetDetailsVo> associateOccupiedBudgetContractDetails(Long projectWbsReceiptsId, String wbsSummaryCode) {
        List<AssociateOccupiedBudgetDetailsVo> assList = new ArrayList<>();
        Map<String, Object> param = new HashMap<>();
        param.put("projectWbsReceiptsId", projectWbsReceiptsId);
        param.put("wbsSummaryCode", wbsSummaryCode);
        param.put("purchaseType", PurchaseMaterialRequirementPurchaseTypeEnums.OUTSOURCE.getCode());
        List<PurchaseContractBudgetDto> wbsContractBudgetList = purchaseContractBudgetExtMapper.getWbsContractBudgetByParam(param);
        if (ListUtils.isEmpty(wbsContractBudgetList)) {
            return assList;
        }
        // 根据采购合同汇总
        Map<Long, List<PurchaseContractBudgetDto>> contractDetailMap = wbsContractBudgetList.stream().collect(Collectors.groupingBy(PurchaseContractBudgetDto::getPurchaseContractId));
        contractDetailMap.entrySet().forEach(entry -> {
            List<PurchaseContractBudgetDto> contractDetailDtoList = entry.getValue();
            PurchaseContractBudgetDto contractDetail = contractDetailDtoList.get(0);
            AssociateOccupiedBudgetDetailsVo assVo = new AssociateOccupiedBudgetDetailsVo();
            assVo.setId(contractDetail.getPurchaseContractId());
            assVo.setOrderNo(contractDetail.getContractCode());
            assVo.setStatus(contractDetail.getContractStatus());
            assVo.setDiscountMoney(contractDetail.getContractTotalAmount());
            assList.add(assVo);
        });
        return assList;
    }

    @Override
    public List<PurchaseDemandOrderIssudeVo> getMergeOrder(String id) {
        String json = String.valueOf(redisUtilServer.get(RedisContant.PURCHASE_DEMAND_ORDER_ISSUED + id));
        List<PurchaseDemandOrderIssudeVo> list = JSON.parseArray(json, PurchaseDemandOrderIssudeVo.class);
        return list;
    }

    public List<PurchaseOrderDetailDto> buildPurchaseOrderDetailList(List<PurchaseDemandOrderIssudeDto> list) {
        List<PurchaseOrderDetailDto> purchaseOrderDetailList = new ArrayList<>();
        for (PurchaseDemandOrderIssudeDto purchaseDemandOrderIssudeDto : list) {
            PurchaseOrderDetailDto detailDto = new PurchaseOrderDetailDto();
            detailDto.setErpCode(purchaseDemandOrderIssudeDto.getErpCode());
            detailDto.setOrgId(purchaseDemandOrderIssudeDto.getOrgId());
            purchaseOrderDetailList.add(detailDto);
        }
        return purchaseOrderDetailList;
    }

    @Override
    public Long setMergeOrder(List<PurchaseDemandOrderIssudeDto> list) {
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        //赋值比较字段;     根据"业务实体+是否急单+供应商编码+供应商地点+币种"维度分组生成采购订单
        for (int i = 0; i < list.size(); i++) {
            StringBuffer sb = new StringBuffer();
            String data = sb.append(list.get(i).getDispatchIs())
                    .append(list.get(i).getProjectOuId())
                    .append(list.get(i).getErpVendorSiteId())
                    .append(list.get(i).getCurrencyCode())
                    .toString();
            list.get(i).setData(data);
        }

        //查询本位币
        OrganizationRelDto organizationRel = projectBusinessService.findOrganizationRel(list.get(0).getProjectOuId());
        String localCurrency = Optional.ofNullable(organizationRel).map(OrganizationRelDto::getCurrency).orElse(null);

        //进入订单下达页面，汇率日期默认值为当天
        Date conversionDate = DateUtil.getBeginningOfDay(new Date());

        //批量查询物料历史价格
        List<Long> orgErrorList = new ArrayList<>();
       // Map<String, BigDecimal> materialPriceMap = this.getMaterialPriceMap(buildPurchaseOrderDetailList(list), orgErrorList);
        Map<String, MaterialPrice> materialPriceEntityMap = this.getMaterialPriceEntityMap(buildPurchaseOrderDetailList(list), orgErrorList);
        //分组
        Map<String, List<PurchaseDemandOrderIssudeDto>> groupMap = list.stream().
                collect(Collectors.groupingBy(PurchaseDemandOrderIssudeDto::getData));

        //返回结果
        List<PurchaseDemandOrderIssudeVo> purchaseDemandOrderIssudeVos = new ArrayList<>();
        Map<String, BigDecimal> glDailyRateMap = new HashMap<>();

        //提取分组
        groupMap.entrySet().stream().map(entry -> {
            PurchaseDemandOrderIssudeVo purchaseDemandOrderIssudeVo = new PurchaseDemandOrderIssudeVo();
            PurchaseDemandOrderIssudeTitleVo purchaseOrderTitle = new PurchaseDemandOrderIssudeTitleVo();
            List<PurchaseDemandOrderIssudeListVo> vendorSiteBankArrayList = new ArrayList<>();

            for (int i = 0; i < entry.getValue().size(); i++) {
                PurchaseDemandOrderIssudeListVo vendorSiteBank = new PurchaseDemandOrderIssudeListVo();
                BeanUtils.copyProperties(entry.getValue().get(i), vendorSiteBank);
                setHistoryPrice(vendorSiteBank, materialPriceEntityMap, glDailyRateMap, localCurrency, conversionDate, orgErrorList);
                vendorSiteBankArrayList.add(vendorSiteBank);
            }

            BeanUtils.copyProperties(entry.getValue().get(0), purchaseOrderTitle);
            purchaseDemandOrderIssudeVo.setPurchaseOrderTitle(purchaseOrderTitle);
            purchaseDemandOrderIssudeVo.setVendorSiteBanks(vendorSiteBankArrayList);
            purchaseDemandOrderIssudeVos.add(purchaseDemandOrderIssudeVo);
            return entry;
        }).collect(Collectors.toList());

        //公式：订单折扣后金额（不含税）=合并后采购订单的折后价（不含税）*【各合并前采购订单行的 （下达数量-取消数量）】
        //（合并订单行的最后一行计算：订单折扣后金额（不含税）-已分配的金额）
        //折后金额(前端计算)
        BigDecimal discountMoney = new BigDecimal(0);


        String id = GenerateIDUtils.generateSixteenBitID();
        //listTojson
        String json = JSONArray.toJSON(purchaseDemandOrderIssudeVos).toString();
        //缓存  有效期12小时
        redisUtilServer.set(PURCHASE_DEMAND_ORDER_ISSUED + id, json, 60 * 60 * 12L);
        return Long.valueOf(id);
    }

    /**
     * 设置采购订单历史价
     *
     * @param vo
     * @param materialPriceMap
     */
    public void setHistoryPrice(PurchaseDemandOrderIssudeListVo vo,
                                Map<String, MaterialPrice> materialPriceMap,
                                Map<String, BigDecimal> glDailyRateMap,
                                String localCurrency,
                                Date conversionDate,
                                List<Long> orgErrorList) {
        if (!orgErrorList.contains(vo.getOrgId())) {
            //对应库存组织下该物料的“最新价格”
            MaterialPrice materialPrice = materialPriceMap.get(buildMaterialPriceGroupKey(vo.getErpCode(), vo.getOrgId()));
            BigDecimal amount = materialPrice.getAmount();
            if (amount != null) {
                BigDecimal exchangeRate = null;
                if (!Objects.equals(vo.getCurrencyCode(), localCurrency) && glDailyRateMap != null) {
                    BigDecimal glDailyRate = glDailyRateMap.get(vo.getCurrencyCode());
                    if (glDailyRate == null) {
                        GlDailyRateQuery query = new GlDailyRateQuery();
                        query.setFromCurrency(vo.getCurrencyCode());
                        query.setToCurrency(localCurrency);
                        query.setExchangeDate(conversionDate);
                        query.setExchangeType(CorUserStatus.Corporate.getCode()); //默认值
                        GlDailyRateDto glDailyRateDto = basedataExtService.queryGlDailyRate(query);
                        if (glDailyRateDto != null && glDailyRateDto.getExchangeRate() != null) {
                            exchangeRate = glDailyRateDto.getExchangeRate();
                            glDailyRateMap.put(vo.getCurrencyCode(), exchangeRate);
                        }
                    } else {
                        exchangeRate = glDailyRate;
                    }
                } else {
                    exchangeRate = BigDecimal.ONE;
                }
                //历史价-本位币
                vo.setStandardHistoryPrice(amount);
                //历史价
                if (exchangeRate != null) {
                    vo.setHistoryPrice(BigDecimalUtils.divide(amount, exchangeRate));
                }
                //设置历史价对应的订单行ID,项目编号,项目名称
                Long historyOrderDetailId = materialPrice.getPurchaseBpaPriceId();
                if(Objects.nonNull(historyOrderDetailId)){
                    vo.setHistoryPriceOrderDetailId(materialPrice.getPurchaseBpaPriceId());
                    PurchaseOrderDetail historyOrderDetail = purchaseOrderDetailMapper.selectByPrimaryKey(historyOrderDetailId);
                    if(Objects.nonNull(historyOrderDetail)){
                        Long historyOrderDetailProjectId = historyOrderDetail.getProjectId();
                        Project historyOrderDetailProject = projectMapper.selectByPrimaryKey(historyOrderDetailProjectId);
                        if(Objects.nonNull(historyOrderDetailProject)){
                            vo.setHistoryPriceProjectCode(historyOrderDetailProject.getCode());
                            vo.setHistoryPriceProjectName(historyOrderDetailProject.getName());
                        }
                    }
                }
            } else {
                vo.setErrorMsg("历史价不存在");
            }
        } else {
            vo.setErrorMsg("组织参数：WBS采购订单历史价价格类型，配置有误，请联系IT处理");
        }
    }

    /**
     * 通过 WBS采购订单历史价价格类型 查询物料历史价格
     *
     * @return
     */
    @Override
    public Map<String, BigDecimal> getMaterialPriceMap(List<PurchaseOrderDetailDto> detailList,
                                                       List<Long> orgErrorList) {

        List<Long> organizationIdList = detailList.stream().map(s -> s.getOrgId()).distinct().collect(Collectors.toList());
        List<String> erpCodeList = detailList.stream().map(s -> s.getErpCode()).distinct().collect(Collectors.toList());
        List<String> materialPriceGroupKeyList =
                detailList.stream().map(s -> buildMaterialPriceGroupKey(s.getErpCode(), s.getOrgId())).collect(Collectors.toList());
        Map<String, BigDecimal> materialPriceMap = new HashMap<>();
        if (CollectionUtils.isEmpty(organizationIdList) || CollectionUtils.isEmpty(erpCodeList) || CollectionUtils.isEmpty(materialPriceGroupKeyList)) {
            return materialPriceMap;
        }
        Map<Long, String> configHeaderNameMap = new HashMap<>();
        for (Long organizationId : organizationIdList) {
            // 查询组织参数：WBS采购订单历史价价格类型
            Set<String> materialPriceConfigSet = organizationCustomDictService.queryByName(Constants.WBS_PURCHASE_ORDER_MATERIAL_PRICE_CONFIG,
                    organizationId, OrgCustomDictOrgFrom.INVENTORY_ORG);
            if (CollectionUtils.isNotEmpty(materialPriceConfigSet) && materialPriceConfigSet.size() == 1
                    && StringUtils.isNotEmpty(materialPriceConfigSet.iterator().next())) {
                String configHeaderName = materialPriceConfigSet.iterator().next();
                configHeaderNameMap.put(organizationId, configHeaderName);
            } else {
                orgErrorList.add(organizationId);
            }
        }
        if (!configHeaderNameMap.isEmpty()) {
            MaterialPriceDto query = new MaterialPriceDto();
            query.setConfigHeaderNameMap(configHeaderNameMap);
            query.setOrganizationIdList(organizationIdList);
            query.setErpCodeList(erpCodeList);
            query.setMaterialPriceGroupKeyList(materialPriceGroupKeyList);
            materialPriceMap.putAll(basedataExtService.getMaterialPriceByConfigHeader(query));
        }
        return materialPriceMap;
    }


    /**
     * 通过 WBS采购订单历史价价格类型 查询物料历史价格实体信息
     *
     * @return
     */
    @Override
    public Map<String, MaterialPrice> getMaterialPriceEntityMap(List<PurchaseOrderDetailDto> detailList,
                                                                List<Long> orgErrorList) {

        List<Long> organizationIdList = detailList.stream().map(s -> s.getOrgId()).distinct().collect(Collectors.toList());
        List<String> erpCodeList = detailList.stream().map(s -> s.getErpCode()).distinct().collect(Collectors.toList());
        List<String> materialPriceGroupKeyList =
                detailList.stream().map(s -> buildMaterialPriceGroupKey(s.getErpCode(), s.getOrgId())).collect(Collectors.toList());
        Map<String, MaterialPrice> materialPriceMap = new HashMap<>();
        if (CollectionUtils.isEmpty(organizationIdList) || CollectionUtils.isEmpty(erpCodeList) || CollectionUtils.isEmpty(materialPriceGroupKeyList)) {
            return materialPriceMap;
        }
        Map<Long, String> configHeaderNameMap = new HashMap<>();
        for (Long organizationId : organizationIdList) {
            // 查询组织参数：WBS采购订单历史价价格类型
            Set<String> materialPriceConfigSet = organizationCustomDictService.queryByName(Constants.WBS_PURCHASE_ORDER_MATERIAL_PRICE_CONFIG,
                    organizationId, OrgCustomDictOrgFrom.INVENTORY_ORG);
            if (CollectionUtils.isNotEmpty(materialPriceConfigSet) && materialPriceConfigSet.size() == 1
                    && StringUtils.isNotEmpty(materialPriceConfigSet.iterator().next())) {
                String configHeaderName = materialPriceConfigSet.iterator().next();
                configHeaderNameMap.put(organizationId, configHeaderName);
            } else {
                orgErrorList.add(organizationId);
            }
        }
        if (!configHeaderNameMap.isEmpty()) {
            MaterialPriceDto query = new MaterialPriceDto();
            query.setConfigHeaderNameMap(configHeaderNameMap);
            query.setOrganizationIdList(organizationIdList);
            query.setErpCodeList(erpCodeList);
            query.setMaterialPriceGroupKeyList(materialPriceGroupKeyList);
            materialPriceMap.putAll(basedataExtService.getMaterialPriceEntityByConfigHeader(query));
        }
        return materialPriceMap;
    }

    private String buildMaterialPriceGroupKey(String erpCode, Long organizationId) {
        return String.format("buildMaterialPriceGroupKey_%s_%s", erpCode, organizationId);
    }

    @Override
    public List<PurchaseOrderDetailDto> queryHistoryPrice(List<PurchaseOrderDetailDto> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        //批量查询采购订单
        List<Long> orderIdList = list.stream().map(s -> s.getPurchaseOrderId()).collect(Collectors.toList());
        PurchaseOrderExample orderExample = new PurchaseOrderExample();
        orderExample.createCriteria().andIdIn(orderIdList);
        Map<Long, BigDecimal> conversionRateMap =
                purchaseOrderMapper.selectByExample(orderExample).stream().collect(Collectors.toMap(s -> s.getId(), s -> s.getConversionRate()));

        //批量查询物料历史价格
        List<Long> orgErrorList = new ArrayList<>();
        Map<String, BigDecimal> materialPriceMap = this.getMaterialPriceMap(list, orgErrorList);
        for (PurchaseOrderDetailDto detailDto : list) {
            if (!orgErrorList.contains(detailDto.getOrgId())) {
                //用物料价格表取到的“最新价格”/订单上的汇率，作为该物料的历史价
                BigDecimal conversionRate = conversionRateMap.getOrDefault(detailDto.getPurchaseOrderId(), BigDecimal.ONE);
                BigDecimal amount = materialPriceMap.get(buildMaterialPriceGroupKey(detailDto.getErpCode(), detailDto.getOrgId()));
                if (amount != null) {
                    detailDto.setHistoryPrice(BigDecimalUtils.divide(amount, conversionRate));
                } else {
                    detailDto.setErrorMsg("历史价不存在");
                }
            } else {
                detailDto.setErrorMsg("组织参数：WBS采购订单历史价价格类型，配置有误，请联系IT处理");
            }
        }
        return list;
    }

    @Override
    public PurchaseOrderDetailDto setGetMergeOrderDetail(List<PurchaseOrderDetailDto> list) {
        //代码冗余,思路清晰;
        boolean isNot = true;
        boolean isAll = true;
        for (int i = 0; i < list.size(); i++) {
            if (!StringUtils.isEmpty(list.get(i).getChildren()) || !ListUtils.isEmpty(list.get(i).getChildren())) {
                //有一个子节点,就不是第一次
                isNot = false;
            }
            if (StringUtils.isEmpty(list.get(i).getChildren()) || ListUtils.isEmpty(list.get(i).getChildren())) {
                //有一个没子节点,就不是
                isAll = false;
            }
        }
        if (isNot) {
            //都没子节点,第一次合并
            return mergeNot(list);
        }
        if (isAll) {
            //全部都有子节点
            return mergeAll(list);
        }
        //其他情况;
        return mergeOther(list);
    }

    private PurchaseOrderDetailDto mergeNot(List<PurchaseOrderDetailDto> list) {
        List<PurchaseOrderDetailDto> newList = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            PurchaseOrderDetailDto purchaseOrderDetailDto = list.get(i);
            if (purchaseOrderDetailDto != null) {
                purchaseOrderDetailDto.setMergeRows(true);
                purchaseOrderDetailDto.setWbsSummaryCodeStar(0);
                purchaseOrderDetailDto.setDesignReleaseLotNumberStar(0);
                purchaseOrderDetailDto.setRequirementCodeStar(0);
                purchaseOrderDetailDto.setMaterialPurchaseRequirementId(purchaseOrderDetailDto.getId());
                newList.add(purchaseOrderDetailDto);
            }
        }
        for (int i = 0; i < newList.size(); i++) {
            if (String.valueOf(newList.get(0).getUnit()).equals("null")) {
                throw new MipException("单位为null，不允许合并");
            }

            if (!String.valueOf(newList.get(0).getErpCode()).equals(newList.get(i).getErpCode()) ||
                    !String.valueOf(newList.get(0).getProjectId()).equals(String.valueOf(newList.get(i).getProjectId()))
                    || !String.valueOf(newList.get(0).getUnit()).equals(newList.get(i).getUnit())) {
                throw new MipException("项目+ERP物料编码+单位不相同，不允许合并");
            }
        }

        PurchaseOrderDetailDto purchaseOrderDetailDtoResult = new PurchaseOrderDetailDto();
        //"WBS"、"需求发布单据"、"设计发布批次号"
        StringBuffer wbsSummaryCode = new StringBuffer();
        StringBuffer requirementCode = new StringBuffer();
        StringBuffer designReleaseLotNumber = new StringBuffer();
        //"单位"、"图纸版本号"
        String unit = "";
        String chartVersion = "0";
        BigDecimal unitPrice = new BigDecimal(0);
        BigDecimal orderNum = new BigDecimal(0);


        //"WBS"、"需求发布单据"、"设计发布批次号"， 如果值是相同的，不按"*"标记
        boolean compareWbsSummaryCode = false;
        boolean compareRequirementCode = false;
        boolean compareDesignReleaseLotNumber = false;
        for (int i = 0; i < newList.size(); i++) {
            if (!newList.get(0).getWbsSummaryCode().equals(newList.get(i).getWbsSummaryCode())) {
                compareWbsSummaryCode = true;
            }
            if (!newList.get(0).getRequirementCode().equals(newList.get(i).getRequirementCode())) {
                compareRequirementCode = true;
            }
            if (!newList.get(0).getDesignReleaseLotNumber().equals(newList.get(i).getDesignReleaseLotNumber())) {
                compareDesignReleaseLotNumber = true;
            }
        }

        for (int i = 0; i < newList.size(); i++) {
            if (i == 0 && compareWbsSummaryCode) {
                newList.get(i).setWbsSummaryCodeStar(1);
            }
            if (i == 0 && compareRequirementCode) {
                newList.get(i).setRequirementCodeStar(1);
            }
            if (i == 0 && compareDesignReleaseLotNumber) {
                newList.get(i).setDesignReleaseLotNumberStar(1);
            }

            if (i == 0) {
                wbsSummaryCode.append(newList.get(i).getWbsSummaryCode());
                requirementCode.append(newList.get(i).getRequirementCode());
                designReleaseLotNumber.append(newList.get(i).getDesignReleaseLotNumber());

                BeanUtils.copyProperties(newList.get(i), purchaseOrderDetailDtoResult);
                unit = newList.get(i).getUnit();
                chartVersion = newList.get(i).getChartVersion();
            }
            BigDecimal up = StringUtils.isEmpty(newList.get(i).getUnitPrice()) ? new BigDecimal(0) : newList.get(i).getUnitPrice();
            unitPrice = unitPrice.add(up);
            BigDecimal on = StringUtils.isEmpty(newList.get(i).getOrderNum()) ? new BigDecimal(0) : newList.get(i).getOrderNum();
            orderNum = orderNum.add(on);
        }

        // 详情："单位"、"图纸版本号" 返回,用户自己填;
        purchaseOrderDetailDtoResult.setWbsSummaryCode(String.valueOf(wbsSummaryCode));
        purchaseOrderDetailDtoResult.setRequirementCode(String.valueOf(requirementCode));
        purchaseOrderDetailDtoResult.setDesignReleaseLotNumber(String.valueOf(designReleaseLotNumber));
        purchaseOrderDetailDtoResult.setUnit(unit);
        purchaseOrderDetailDtoResult.setChartVersion(chartVersion);
        // 一揽子协议价,取单价赋值(合并数据取都相同取第一条即可)
        PurchaseOrderDetailDto detailDto = list.get(0);
        if (detailDto.getPricingType().equals("2")) {
            purchaseOrderDetailDtoResult.setUnitPrice(detailDto.getUnitPrice());
        } else {
            purchaseOrderDetailDtoResult.setUnitPrice(new BigDecimal(0));
        }
        purchaseOrderDetailDtoResult.setDiscount(new BigDecimal(0));
        purchaseOrderDetailDtoResult.setOrderNum(orderNum);

        //最新发布日期,取最大值
        Optional<PurchaseOrderDetailDto> publishTime_max = newList.stream()
                .filter(Objects::nonNull)
                .filter(item -> item.getPublishTime() != null)
                .max(Comparator.comparing(PurchaseOrderDetailDto::getPublishTime));
        if (publishTime_max.isPresent()) {
            purchaseOrderDetailDtoResult.setPublishTime(publishTime_max.get().getPublishTime());
        }
        purchaseOrderDetailDtoResult.setChildren(newList);
        return purchaseOrderDetailDtoResult;
    }

    private PurchaseOrderDetailDto mergeAll(List<PurchaseOrderDetailDto> list) {
        List<PurchaseOrderDetailDto> newList = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            List<PurchaseOrderDetailDto> purchaseOrderDetailDtoList = list.get(i).getChildren();
            if (purchaseOrderDetailDtoList != null && purchaseOrderDetailDtoList.size() > 0) {
                for (int j = 0; j < purchaseOrderDetailDtoList.size(); j++) {
                    list.get(i).getChildren().get(j).setChildIs(true);
                    newList.add(list.get(i).getChildren().get(j));
                }
            }

            PurchaseOrderDetailDto purchaseOrderDetailDto = list.get(i);
            if (purchaseOrderDetailDto != null) {
                purchaseOrderDetailDto.setChildIs(false);
                newList.add(purchaseOrderDetailDto);
            }
        }

        for (int i = 0; i < newList.size(); i++) {
            if (String.valueOf(newList.get(0).getUnit()).equals("null")) {
                throw new MipException("单位为null，不允许合并");
            }

            if (!String.valueOf(newList.get(0).getErpCode()).equals(newList.get(i).getErpCode()) ||
                    !String.valueOf(newList.get(0).getProjectId()).equals(String.valueOf(newList.get(i).getProjectId()))
                    || !String.valueOf(newList.get(0).getUnit()).equals(newList.get(i).getUnit())) {
                throw new MipException("项目+ERP物料编码+单位不相同，不允许合并");
            }
        }

        PurchaseOrderDetailDto purchaseOrderDetailDtoResult = new PurchaseOrderDetailDto();
        //"WBS"、"需求发布单据"、"设计发布批次号"
        StringBuffer wbsSummaryCode = new StringBuffer();
        StringBuffer requirementCode = new StringBuffer();
        StringBuffer designReleaseLotNumber = new StringBuffer();
        //"单位"、"图纸版本号"
        String unit = "";
        String chartVersion = "0";
        BigDecimal unitPrice = new BigDecimal(0);
        BigDecimal orderNum = new BigDecimal(0);

        boolean flag = true;
        for (int i = 0; i < newList.size(); i++) {
            if (!newList.get(i).getChildIs()) {
                if (flag) {
                    BeanUtils.copyProperties(newList.get(i), purchaseOrderDetailDtoResult);
                    unit = newList.get(i).getUnit();
                    chartVersion = newList.get(i).getChartVersion();
                    wbsSummaryCode.append(newList.get(i).getWbsSummaryCode());
                    requirementCode.append(newList.get(i).getRequirementCode());
                    designReleaseLotNumber.append(newList.get(i).getDesignReleaseLotNumber());
                    flag = false;
                }
                BigDecimal up = StringUtils.isEmpty(newList.get(i).getUnitPrice()) ? new BigDecimal(0) : newList.get(i).getUnitPrice();
                unitPrice = unitPrice.add(up);
                BigDecimal on = StringUtils.isEmpty(newList.get(i).getOrderNum()) ? new BigDecimal(0) : newList.get(i).getOrderNum();
                orderNum = orderNum.add(on);
            }
        }

        // 详情："单位"、"图纸版本号" 返回,用户自己填;
        purchaseOrderDetailDtoResult.setWbsSummaryCode(String.valueOf(wbsSummaryCode));
        purchaseOrderDetailDtoResult.setRequirementCode(String.valueOf(requirementCode));
        purchaseOrderDetailDtoResult.setDesignReleaseLotNumber(String.valueOf(designReleaseLotNumber));
        purchaseOrderDetailDtoResult.setUnit(unit);
        purchaseOrderDetailDtoResult.setChartVersion(chartVersion);
        // 一揽子协议价,取单价赋值(合并数据取都相同取第一条即可)
        PurchaseOrderDetailDto detailDto = list.get(0);
        if (detailDto.getPricingType().equals("2")) {
            purchaseOrderDetailDtoResult.setUnitPrice(detailDto.getUnitPrice());
        } else {
            purchaseOrderDetailDtoResult.setUnitPrice(new BigDecimal(0));
        }
        purchaseOrderDetailDtoResult.setDiscount(new BigDecimal(0));
        purchaseOrderDetailDtoResult.setOrderNum(orderNum);

        //最新发布日期,取最大值
        Optional<PurchaseOrderDetailDto> publishTime_max = newList.stream()
                .filter(Objects::nonNull)
                .filter(item -> item.getPublishTime() != null)
                .max(Comparator.comparing(PurchaseOrderDetailDto::getPublishTime));
        if (publishTime_max.isPresent()) {
            purchaseOrderDetailDtoResult.setPublishTime(publishTime_max.get().getPublishTime());
        }
        //去除主对象;
        List<PurchaseOrderDetailDto> resultList = new ArrayList<>();
        for (PurchaseOrderDetailDto purchaseOrderDetailDto : newList) {
            if (purchaseOrderDetailDto.getChildIs()) {
                purchaseOrderDetailDto.setMergeRows(true);
                purchaseOrderDetailDto.setMaterialPurchaseRequirementId(purchaseOrderDetailDto.getId());
                resultList.add(purchaseOrderDetailDto);
            }
        }
        purchaseOrderDetailDtoResult.setChildren(resultList);
        return purchaseOrderDetailDtoResult;
    }

    private PurchaseOrderDetailDto mergeOther(List<PurchaseOrderDetailDto> list) {
        List<PurchaseOrderDetailDto> newList = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            List<PurchaseOrderDetailDto> purchaseOrderDetailDtoList = list.get(i).getChildren();
            if (purchaseOrderDetailDtoList != null && purchaseOrderDetailDtoList.size() > 0) {
                for (int j = 0; j < purchaseOrderDetailDtoList.size(); j++) {
                    //默认值
                    list.get(i).getChildren().get(j).setChildIs(true);
                    newList.add(list.get(i).getChildren().get(j));
                }
            }

            PurchaseOrderDetailDto purchaseOrderDetailDto = list.get(i);
            if (purchaseOrderDetailDto != null) {
                //默认值
                purchaseOrderDetailDto.setChildIs(false);
                newList.add(purchaseOrderDetailDto);
            }
        }

        for (int i = 0; i < newList.size(); i++) {
            if (String.valueOf(newList.get(0).getUnit()).equals("null")) {
                throw new MipException("单位为null，不允许合并");
            }

            if (!String.valueOf(newList.get(0).getErpCode()).equals(newList.get(i).getErpCode()) ||
                    !String.valueOf(newList.get(0).getProjectId()).equals(String.valueOf(newList.get(i).getProjectId()))
                    || !String.valueOf(newList.get(0).getUnit()).equals(newList.get(i).getUnit())) {
                throw new MipException("项目+ERP物料编码+单位不相同，不允许合并");
            }
        }

        PurchaseOrderDetailDto purchaseOrderDetailDtoResult = new PurchaseOrderDetailDto();
        //"WBS"、"需求发布单据"、"设计发布批次号"
        StringBuffer wbsSummaryCode = new StringBuffer();
        StringBuffer requirementCode = new StringBuffer();
        StringBuffer designReleaseLotNumber = new StringBuffer();
        //"单位"、"图纸版本号"
        String unit = "";
        String chartVersion = "0";
        BigDecimal unitPrice = new BigDecimal(0);
        BigDecimal orderNum = new BigDecimal(0);

        boolean flag = true;
        for (int i = 0; i < newList.size(); i++) {
            if (!newList.get(i).getChildIs()) {
                if (flag) {
                    BeanUtils.copyProperties(newList.get(i), purchaseOrderDetailDtoResult);
                    unit = newList.get(i).getUnit();
                    chartVersion = newList.get(i).getChartVersion();
                    wbsSummaryCode.append(newList.get(i).getWbsSummaryCode());
                    requirementCode.append(newList.get(i).getRequirementCode());
                    designReleaseLotNumber.append(newList.get(i).getDesignReleaseLotNumber());
                    flag = false;
                }
                BigDecimal up = StringUtils.isEmpty(newList.get(i).getUnitPrice()) ? new BigDecimal(0) : newList.get(i).getUnitPrice();
                unitPrice = unitPrice.add(up);
                BigDecimal on = StringUtils.isEmpty(newList.get(i).getOrderNum()) ? new BigDecimal(0) : newList.get(i).getOrderNum();
                orderNum = orderNum.add(on);
            }
        }

        // 详情："单位"、"图纸版本号" 返回,用户自己填;
        purchaseOrderDetailDtoResult.setWbsSummaryCode(String.valueOf(wbsSummaryCode));
        purchaseOrderDetailDtoResult.setRequirementCode(String.valueOf(requirementCode));
        purchaseOrderDetailDtoResult.setDesignReleaseLotNumber(String.valueOf(designReleaseLotNumber));
        purchaseOrderDetailDtoResult.setUnit(unit);
        purchaseOrderDetailDtoResult.setChartVersion(chartVersion);
        // 一揽子协议价,取单价赋值(合并数据取都相同取第一条即可)
        PurchaseOrderDetailDto detailDto = list.get(0);
        if (detailDto.getPricingType().equals("2")) {
            purchaseOrderDetailDtoResult.setUnitPrice(detailDto.getUnitPrice());
        } else {
            purchaseOrderDetailDtoResult.setUnitPrice(new BigDecimal(0));
        }
        purchaseOrderDetailDtoResult.setDiscount(new BigDecimal(0));
        purchaseOrderDetailDtoResult.setOrderNum(orderNum);

        //最新发布日期,取最大值
        Optional<PurchaseOrderDetailDto> publishTime_max = newList.stream()
                .filter(Objects::nonNull)
                .filter(item -> item.getPublishTime() != null)
                .max(Comparator.comparing(PurchaseOrderDetailDto::getPublishTime));
        if (publishTime_max.isPresent()) {
            purchaseOrderDetailDtoResult.setPublishTime(publishTime_max.get().getPublishTime());
        }
        //去除合并父对象;
        List<PurchaseOrderDetailDto> resultList = new ArrayList<>();
        for (PurchaseOrderDetailDto purchaseOrderDetailDto : newList) {
            if (purchaseOrderDetailDto.getChildIs()) {
                purchaseOrderDetailDto.setMergeRows(true);
                purchaseOrderDetailDto.setMaterialPurchaseRequirementId(purchaseOrderDetailDto.getId());
                resultList.add(purchaseOrderDetailDto);
            }
            if (!purchaseOrderDetailDto.getChildIs()
                    && (StringUtils.isEmpty(purchaseOrderDetailDto.getChildren()) || ListUtils.isEmpty(purchaseOrderDetailDto.getChildren()))) {
                purchaseOrderDetailDto.setMergeRows(true);
                purchaseOrderDetailDto.setMaterialPurchaseRequirementId(purchaseOrderDetailDto.getId());
                resultList.add(purchaseOrderDetailDto);
            }
        }
        purchaseOrderDetailDtoResult.setChildren(resultList);
        return purchaseOrderDetailDtoResult;
    }

    @Override
    public SdpCheckVo sdpCheck(List<SdpCheckDto> sdpCheckDto) {
        SdpCheckVo sdpCheckVo = new SdpCheckVo();
        List<SupplierQualityDeactivation> supplierQualityDeactivationList = supplierQualityDeactivationExtMapper.sdpCheck(sdpCheckDto);
        if (supplierQualityDeactivationList != null && supplierQualityDeactivationList.size() > 0) {
            sdpCheckVo.setErpCode(supplierQualityDeactivationList.get(0).getItemCode());
            sdpCheckVo.setSupplierCode(supplierQualityDeactivationList.get(0).getSupplierCode());
            sdpCheckVo.setOrgId(supplierQualityDeactivationList.get(0).getOrgId());
        }
        return sdpCheckVo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<Long> updateByReceiptsIdStatus(Long receiptsId) {
        List<Long> ids = new ArrayList<>();
        if (String.valueOf(receiptsId).startsWith("1") && String.valueOf(receiptsId).length() == 16) {
            PurchaseOrderExample purchaseOrderExample = new PurchaseOrderExample();
            purchaseOrderExample.createCriteria().andReceiptsIdEqualTo(receiptsId);
            List<PurchaseOrder> list = purchaseOrderMapper.selectByExample(purchaseOrderExample);
            for (PurchaseOrder p : list) {
                PurchaseOrder purchaseOrder = new PurchaseOrder();
                purchaseOrder.setId(p.getId());
                purchaseOrder.setOrderStatus(PurchaseOrderStatus.ABANDON.code());
                purchaseOrderMapper.updateByPrimaryKeySelective(purchaseOrder);

                PurchaseOrderDetailExample purchaseOrderDetailExample = new PurchaseOrderDetailExample();
                purchaseOrderDetailExample.createCriteria().andPurchaseOrderIdEqualTo(purchaseOrder.getId());
                List<PurchaseOrderDetail> purchaseOrderDetailList = purchaseOrderDetailMapper.selectByExample(purchaseOrderDetailExample);
                for (PurchaseOrderDetail purchaseOrderDetail : purchaseOrderDetailList) {
                    PurchaseOrderDetail pod = new PurchaseOrderDetail();
                    pod.setId(purchaseOrderDetail.getId());
                    pod.setStatus(PurchaseOrderDetailStatus.ORDER_CANCELED.code());
                    pod.setCancelNum(purchaseOrderDetail.getOrderNum());
                    purchaseOrderDetailMapper.updateByPrimaryKeySelective(pod);
                }
            }
            ids = list.stream().map(PurchaseOrder::getReceiptsId).collect(Collectors.toList());
            purchaseOrderCallBackService.updateRequirementStatusByOrderId(Long.toString(receiptsId));
        } else {
            PurchaseOrder purchaseOrder = new PurchaseOrder();
            purchaseOrder.setId(receiptsId);
            purchaseOrder.setOrderStatus(PurchaseOrderStatus.ABANDON.code());
            purchaseOrderMapper.updateByPrimaryKeySelective(purchaseOrder);

            PurchaseOrderDetailExample purchaseOrderDetailExample = new PurchaseOrderDetailExample();
            purchaseOrderDetailExample.createCriteria().andPurchaseOrderIdEqualTo(purchaseOrder.getId());
            List<PurchaseOrderDetail> purchaseOrderDetailList = purchaseOrderDetailMapper.selectByExample(purchaseOrderDetailExample);
            for (PurchaseOrderDetail purchaseOrderDetail : purchaseOrderDetailList) {
                PurchaseOrderDetail pod = new PurchaseOrderDetail();
                pod.setId(purchaseOrderDetail.getId());
                pod.setStatus(PurchaseOrderDetailStatus.ORDER_CANCELED.code());
                pod.setCancelNum(purchaseOrderDetail.getOrderNum());
                purchaseOrderDetailMapper.updateByPrimaryKeySelective(pod);
            }

            PurchaseOrder order = purchaseOrderMapper.selectByPrimaryKey(receiptsId);
            ids.add(order.getReceiptsId());
            purchaseOrderCallBackService.updateRequirementStatusByOrderId(Long.toString(purchaseOrderDetailList.get(0).getReceiptsId()));
        }
        return ids;
    }

    private String generateOrderNum(Long orgId) {
        StringBuffer orderNum = new StringBuffer();
        orderNum.append(CodePrefix.PURCHASE_ORDER_1.code());
        if (null == orgId || orgId < 0) {
            throw new MipException("业务实体未设置");
        }
        //OU短码(先查erp组织关系表，没有就查组织参数配置)
        String ouCode = "";
        Map<String, Object> param = new HashMap<>();
        param.put("operatingUnitId", orgId);
        String url = StringUtils.buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "organizationRel/list", param);
        String res = restTemplate.getForObject(url, String.class);
        List<OrganizationRelDto> organizationRelList = JSON.parseObject(res, new TypeReference<PageInfo<OrganizationRelDto>>() {
        }).getList();
        if (!CollectionUtils.isEmpty(organizationRelList)) {
            ouCode = organizationRelList.get(0).getCompanyCode();
        }
        //查组织参数配置
        if (StringUtils.isEmpty(ouCode)) {
            final OrgCustomDictOrgFrom orgCustomDictOrgFrom = OrgCustomDictOrgFrom.getOrgCustomDictOrgFrom(OrgCustomDictOrgFrom.OU.code());
            if (orgCustomDictOrgFrom == null) {
                throw new MipException("类型参数有误,orgFrom为空");
            }
            List<OrganizationCustomDict> organizationCustomDictList = organizationCustomDictService.queryByOrdId(orgId, new String(),
                    orgCustomDictOrgFrom);
            if (!CollectionUtils.isEmpty(organizationCustomDictList)) {
                Iterator<OrganizationCustomDict> orgIt = organizationCustomDictList.iterator();
                while (orgIt.hasNext()) {
                    OrganizationCustomDict organizationCustomDict = orgIt.next();
                    if (Constants.OU_CODE.equals(organizationCustomDict.getName())) {
                        ouCode = organizationCustomDict.getValue();
                        break;
                    }
                }
            }
        }
        if (StringUtils.isEmpty(ouCode)) {
            throw new MipException("业务实体未配置组织参数OU短码");
        }
        orderNum.append(ouCode);
        orderNum.append(CacheDataUtils.generateSequence(Constants.CODE_ORDER_NUM_LENGTH,
                CodePrefix.PURCHASE_ORDER_1.code() + CodePrefix.PURCHASE_ORDER_2.code() + ouCode, DateUtil.DATE_YYMMDD_PATTERN));
        return orderNum.toString();
    }

    /**
     * 查询满足条件的订单跟合并明细,取当前最新一揽子分段价,计算后更新其对应(单价、折后价、折后)金额
     *
     * @param dto 订单头
     */
    private void updateOrderPrice(PurchaseOrderDto dto) {
        PurchaseOrderDetailDto orderDetailQuery = new PurchaseOrderDetailDto();
        orderDetailQuery.setPurchaseOrderId(dto.getId());
        orderDetailQuery.setOrderStatus(dto.getOrderStatus());
        // 查询订单明细
        List<PurchaseOrderDetailDto> orderDetailDtos = purchaseOrderDetailService.selectList(orderDetailQuery);
        if (CollectionUtil.isEmpty(orderDetailDtos)) {
            return;
        }
        List<PurchaseOrderDetail> orderDetailSaveList = new ArrayList<>();
        orderDetailDtos.forEach(detailDto -> {
            PurchaseBpaPrice purchaseBpaPrice = purchaseBpaPriceService.getPurchaseBpaPrice(build(dto, detailDto));
            // 一揽子协议为空,分段价设为0
            BigDecimal priceOverride = Objects.isNull(purchaseBpaPrice) ? BigDecimal.ZERO : purchaseBpaPrice.getPriceOverride();
            // 折后价: 单价*(1-折扣%)
            BigDecimal newDiscountPrice = calculateDiscountedPrice(priceOverride, detailDto.getDiscount());
            // 折后金额*下单数量
            BigDecimal newDiscountMoney = newDiscountPrice.multiply(detailDto.getOrderNum());
            PurchaseOrderDetail detail = new PurchaseOrderDetail();
            detail.setUnitPrice(priceOverride);
            detail.setDiscountPrice(newDiscountPrice);
            detail.setDiscountMoney(newDiscountMoney);
            detail.setId(detailDto.getId());
            orderDetailSaveList.add(detail);
        });

        if (CollectionUtil.isEmpty(orderDetailSaveList)) {
            return;
        }
        // 根据id,批量更新(单价、折后价、折后金额)
        purchaseOrderDetailExtMapper.updateBatchPriceById(orderDetailSaveList);

        // key - value(priceOverride)
        Map<Long, BigDecimal> unitPriceMap = orderDetailDtos.stream().collect(Collectors.toMap(PurchaseOrderDetail::getId,
                PurchaseOrderDetail::getUnitPrice));
        List<Long> orderDetailIdList = orderDetailDtos.stream().map(PurchaseOrderDetail::getPurchaseOrderId).collect(Collectors.toList());
        // 查询合并明细
        List<PurchaseOrderMerge> orderMergeList = purchaseOrderMergeExtMapper.getOrderMergePriceByDetailId(orderDetailIdList);
        if (CollectionUtil.isNotEmpty(orderMergeList)) {
            // 计算合并明细价格(单价、折后价、折后金额)
            orderMergeList.forEach(orderMerge -> {
                BigDecimal priceOverride = unitPriceMap.get(orderMerge.getPurchaseOrderId());
                BigDecimal newDiscountPrice = calculateDiscountedPrice(priceOverride, orderMerge.getDiscount());
                orderMerge.setUnitPrice(priceOverride);
                orderMerge.setDiscountPrice(newDiscountPrice);
                BigDecimal orderNum = orderMerge.getOrderNum();
                BigDecimal newDiscountMoney = newDiscountPrice.multiply(orderNum);
                orderMerge.setDiscountMoney(newDiscountMoney);
            });
            // 根据id,批量更新合并明细(单价、折后价、折后金额)
            purchaseOrderMergeExtMapper.updateBatchPriceById(orderMergeList);
        }
    }

    /**
     * 根据需求发布单据回写采购订单行预算占用金额
     *
     * @param publishReceiptIdList 需求发布单据id
     */
    public void updateBudgetOccupiedAmount(List<Long> publishReceiptIdList) {
        if (CollectionUtils.isEmpty(publishReceiptIdList)) {
            return;
        }

        String lockName = "PurchaseOrderServiceImpl_updateBudgetOccupiedAmount";
        String currentDateStr = DateUtil.format(new Date(), "yyyyMMdd");
        try {
            if (DistributedCASLock.lock(lockName, currentDateStr, MAX_WAIT_TIME, MAX_WAIT_TIME * 2)) {
                // 更新采购订单行表
                List<PurchaseOrderDetail> orderDetailList = purchaseOrderDetailExtMapper.getNeedUpdateBudgetOccupiedAmount(publishReceiptIdList);
                if (CollectionUtils.isNotEmpty(orderDetailList)) {
                    purchaseOrderDetailExtMapper.batchUpdate(orderDetailList);
                }

                // 更新采购订单合并行表
                List<PurchaseOrderMerge> mergeList = purchaseOrderDetailExtMapper.getNeedUpdateMergeBudgetOccupiedAmount(publishReceiptIdList);
                if (CollectionUtils.isNotEmpty(mergeList)) {
                    purchaseOrderMergeExtMapper.batchUpdate(mergeList);
                }
            }
        } catch (Exception e) {
            logger.error("updateBudgetOccupiedAmount加锁失败", e);
            throw e;
        } finally {
            DistributedCASLock.unLock(lockName, currentDateStr);
        }

        /*// 更新采购订单行表
        purchaseOrderDetailExtMapper.updateBudgetOccupiedAmount(publishReceiptIdList);

        // 更新采购订单合并行表
        purchaseOrderDetailExtMapper.updateMergeBudgetOccupiedAmount(publishReceiptIdList);*/
    }

    @Override
    public List<PurchaseOrderChangeHistoryDto> getChangingOrder(Long buyerBy) {
        Guard.notNull(buyerBy, "采购员信息不能为空");

        List<Long> ouIdList = SystemContext.getOus();
        if (CollectionUtils.isEmpty(ouIdList)) {
            return new ArrayList<>();
        }
        //订单头
        PurchaseOrderChangeHistoryExample example = new PurchaseOrderChangeHistoryExample();
        example.createCriteria().andCreateByEqualTo(buyerBy)
                .andOuIdIn(ouIdList)
                .andOrderStatusEqualTo(PurchaseOrderStatus.CHANGING.code())
                .andHistoryTypeEqualTo(HistoryType.CHANGE.getCode())
                .andDeletedFlagEqualTo(DeletedFlag.VALID.code());
        List<PurchaseOrderChangeHistoryDto> orderChangeHistoryList = BeanConverter.copy(purchaseOrderChangeHistoryMapper.selectByExample(example),
                PurchaseOrderChangeHistoryDto.class);
        if (CollectionUtils.isEmpty(orderChangeHistoryList)) return new ArrayList<>();
        orderChangeHistoryList.forEach(s -> {
            s.setProjectOuName(CacheDataUtils.findOuNameById(s.getOuId()));
            s.setBuyerName(CacheDataUtils.findUserNameById(s.getBuyerId()));
            s.setErpBuyerName(getErpBuyerName(s.getErpBuyerId()));
            s.setCreateAt(s.getOriginCreateAt());   //原订单头创建日期
        });

        //订单行
        PurchaseOrderDetailChangeHistoryExample detailExample = new PurchaseOrderDetailChangeHistoryExample();
        detailExample.createCriteria().andCreateByEqualTo(buyerBy)
                .andChangeStatusEqualTo(PurchaseOrderDetailChangeHistoryChangeStatus.CHANGING.code())
                .andHistoryTypeEqualTo(HistoryType.CHANGE.getCode())
                .andDeletedFlagEqualTo(DeletedFlag.VALID.code());
        List<PurchaseOrderDetailChangeHistoryDto> orderDetailChangeHistoryList =
                BeanConverter.copy(purchaseOrderDetailChangeHistoryMapper.selectByExample(detailExample), PurchaseOrderDetailChangeHistoryDto.class);
        orderDetailChangeHistoryList.sort(Comparator.comparing(PurchaseOrderDetailChangeHistoryDto::getLineNumber,
                Comparator.nullsLast(Integer::compareTo))); //排序
        List<Long> requirementIds =
                orderDetailChangeHistoryList.stream().map(PurchaseOrderDetailChangeHistoryDto::getMaterialPurchaseRequirementId).collect(Collectors.toList());

        //批量查询最新发布日期
        Map<Long, Date> publishTimeMap = new HashMap<>();
        PurchaseMaterialReleaseDetailExample releaseDetailExample = new PurchaseMaterialReleaseDetailExample();
        releaseDetailExample.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andPurchaseRequirementIdIn(requirementIds);
        List<PurchaseMaterialReleaseDetail> releaseDetailList = purchaseMaterialReleaseDetailMapper.selectByExample(releaseDetailExample);
        Map<Long, List<PurchaseMaterialReleaseDetail>> releaseDetailMap = ListUtils.isEmpty(releaseDetailList) ? new HashMap<>()
                : releaseDetailList.stream().collect(Collectors.groupingBy(PurchaseMaterialReleaseDetail::getPurchaseRequirementId));
        for (Long purchaseRequirementId : releaseDetailMap.keySet()) {
            List<PurchaseMaterialReleaseDetail> detailList = releaseDetailMap.get(purchaseRequirementId);
            PurchaseMaterialReleaseDetail detail =
                    detailList.stream().max(Comparator.comparing(PurchaseMaterialReleaseDetail::getPublishTime)).orElse(null);
            publishTimeMap.put(purchaseRequirementId, Optional.ofNullable(detail).map(PurchaseMaterialReleaseDetail::getPublishTime).orElse(null));
        }

        //数据封装返回
        orderDetailChangeHistoryList.forEach(s -> {
            if (s.getOriginId() == null) { //新增行
                s.setPublishTime(publishTimeMap.get(s.getMaterialPurchaseRequirementId())); //最新发布日期
                s.setCreateByName(CacheDataUtils.findUserNameById(s.getCreateBy()));
            } else {
                s.setCreateAt(s.getOriginCreateAt());          //原订单行创建时间
                s.setCreateByName(s.getOriginCreateByName());  //原订单行创建人
            }
        });
        Map<Long, List<PurchaseOrderDetailChangeHistoryDto>> orderDetailChangeHistoryMap =
                orderDetailChangeHistoryList.stream().collect(Collectors.groupingBy(PurchaseOrderDetailChangeHistoryDto::getPurchaseOrderId));
        orderChangeHistoryList.forEach(s -> s.setPurchaseOrderDetailChangeHistoryDtos(orderDetailChangeHistoryMap.get(s.getOriginId())));
        return orderChangeHistoryList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean changeWithDetailBatch(PurchaseOrderChangeRecordDto recordDto, Long userBy) {
        Asserts.notEmpty(recordDto.getPurchaseOrderChangeHistoryDtos(), ErrorCode.CTC_ORDER_NOT_NULL);
        List<Long> purchaseOrderIdList = new ArrayList<>();
        List<Long> requirementIdList = new ArrayList<>();
        List<PurchaseOrderDetailChangeHistory> orderDetailChangeHistoryList = new ArrayList<>();

        String lockName = String.format("purchaseOrder_changeWithDetailBatch_%s", userBy);
        String currentDateStr = DateUtil.format(new Date(), "yyyyMMdd");
        try {
            if (DistributedCASLock.lock(lockName, currentDateStr, MAX_WAIT_TIME, MAX_WAIT_TIME * 2)) {
                //提取所有的新增行
                List<PurchaseOrderDetail> orderDetailNewList = new ArrayList<>();
                for (PurchaseOrderChangeHistoryDto dto : recordDto.getPurchaseOrderChangeHistoryDtos()) {
                    List<PurchaseOrderDetailChangeHistoryDto> orderDetailChangeHistoryDtos = dto.getPurchaseOrderDetailChangeHistoryDtos();
                    if (CollectionUtils.isNotEmpty(orderDetailChangeHistoryDtos)) {
                        //获取订单行现今最大行号
                        Integer maxLineNum =
                                orderDetailChangeHistoryDtos.stream().map(PurchaseOrderDetailChangeHistoryDto::getLineNumber).filter(Objects::nonNull).max(Comparator.comparingInt(Integer::intValue)).orElse(0);
                        List<PurchaseOrderDetailChangeHistoryDto> detailNewList =
                                orderDetailChangeHistoryDtos.stream().filter(s -> s.getOriginId() == null).collect(Collectors.toList());
                        for (PurchaseOrderDetailChangeHistoryDto detailNew : detailNewList) {
                            detailNew.setLineNumber(++maxLineNum);
                        }
                        orderDetailNewList.addAll(BeanConverter.copy(detailNewList, PurchaseOrderDetail.class));
                        purchaseOrderIdList.add(dto.getOriginId());
                    }
                }

                //提交时，要校验提交的数据是否已经被移除，是，则报提示：数据有变动，请刷新
                List<Long> orderDetailNewIds = orderDetailNewList.stream().map(PurchaseOrderDetail::getId).collect(Collectors.toList());
                PurchaseOrderDetailChangeHistoryExample checkExample = new PurchaseOrderDetailChangeHistoryExample();
                checkExample.createCriteria().andIdIn(orderDetailNewIds).andDeletedFlagEqualTo(DeletedFlag.VALID.code());
                List<PurchaseOrderDetailChangeHistory> detailNewList = purchaseOrderDetailChangeHistoryMapper.selectByExample(checkExample);
                if (detailNewList.size() < orderDetailNewList.size()) {
                    throw new MipException("数据有变动，请刷新");
                }
                //校验是否重复提交
                if (detailNewList.stream().anyMatch(s -> s.getRecordId() != null)) {
                    throw new MipException("订单已变更，请勿重复操作");
                }

                //生成变更记录
                PurchaseOrderChangeRecord record = new PurchaseOrderChangeRecord();
                record.setChangeId(SystemContext.getUserId());
                record.setChangeName(SystemContext.getUserName());
                record.setChangeType("新增订单行");
                record.setStatus(PurchaseOrderChangeRecordStatus.APPROVED.code());
                record.setSyncStatus(PurchaseOrderSyncStatus.NOT_SYNCED.code());
                record.setDeletedFlag(DeletedFlag.VALID.code());
                purchaseOrderChangeRecordMapper.insertSelective(record);
                Long recordId = record.getId();

                //新增行回写正式表
                if (CollectionUtils.isNotEmpty(orderDetailNewList)) {
                    for (PurchaseOrderDetail orderDetail : orderDetailNewList) {
                        orderDetail.setId(null);
                        orderDetail.setStatus(PurchaseOrderDetailStatus.ORDER_PLACED.code());
                        orderDetail.setRecordId(recordId);
                        orderDetail.setCreateAt(null);
                        orderDetail.setUpdateBy(null);
                        orderDetail.setUpdateAt(null);
                        orderDetail.setSyncDeliveryInfoStatus(SyncDeliveryInfoStatusEnum.SYNC_PREPARE.getCode());
                        requirementIdList.add(orderDetail.getMaterialPurchaseRequirementId());
                    }
                    purchaseOrderDetailExtMapper.batchInsert(orderDetailNewList);

                    //处理历史收货信息
                    for (PurchaseOrderDetail purchaseOrderDetail : orderDetailNewList) {
                        purchaseOrderDetailService.batchUpdateDeliveryAddress(Lists.newArrayList(purchaseOrderDetail.getId())
                                , purchaseOrderDetail.getDeliveryAddress(), purchaseOrderDetail.getConsignee(), purchaseOrderDetail.getContactPhone(), false);
                    }

                }

                //更新订单状态
                List<PurchaseOrder> purchaseOrderList = new ArrayList<>();
                for (Long orderId : purchaseOrderIdList) {
                    PurchaseOrder purchaseOrder = new PurchaseOrder();
                    purchaseOrder.setId(orderId);
                    purchaseOrder.setOrderStatus(PurchaseOrderStatus.APPROVED.code());
                    purchaseOrder.setSyncStatus(PurchaseOrderSyncStatus.CHANGE_IN_SYNC.code());
                    purchaseOrderList.add(purchaseOrder);
                }
                if (CollectionUtils.isNotEmpty(purchaseOrderList)) {
                    purchaseOrderExtMapper.batchUpdate(purchaseOrderList);
                }

                //更新变更订单头状态
                List<PurchaseOrderChangeHistoryDto> orderChangeHistoryList = recordDto.getPurchaseOrderChangeHistoryDtos();
                if (CollectionUtils.isNotEmpty(orderChangeHistoryList)) {
                    for (PurchaseOrderChangeHistoryDto orderChangeHistory : orderChangeHistoryList) {
                        orderChangeHistory.setOrderStatus(PurchaseOrderStatus.APPROVED.code());
                        orderChangeHistory.setRecordId(recordId);
                        orderDetailChangeHistoryList.addAll(orderChangeHistory.getPurchaseOrderDetailChangeHistoryDtos());
                    }
                    purchaseOrderChangeHistoryExtMapper.batchUpdate(BeanConverter.copy(orderChangeHistoryList, PurchaseOrderChangeHistory.class));
                }

                //更新变更订单行状态
                if (CollectionUtils.isNotEmpty(orderDetailChangeHistoryList)) {
                    for (PurchaseOrderDetailChangeHistory orderDetailChangeHistory : orderDetailChangeHistoryList) {
                        if (orderDetailChangeHistory.getOriginId() == null) {
                            orderDetailChangeHistory.setStatus(PurchaseOrderDetailStatus.ORDER_PLACED.code());
                        }
                        orderDetailChangeHistory.setChangeStatus(PurchaseOrderDetailChangeHistoryChangeStatus.CHANGED.code());
                        orderDetailChangeHistory.setRecordId(recordId);
                    }
                    purchaseOrderDetailChangeHistoryExtMapper.batchUpdate(orderDetailChangeHistoryList);
                }

                //保存历史版本
                saveHistoryList(purchaseOrderIdList, recordId, userBy);

                //更新采购需求状态
                if (CollectionUtils.isNotEmpty(requirementIdList)) {
                    purchaseMaterialRequirementService.batchUpdateStatus(requirementIdList);
                }

                //同步到ERP
                for (Long orderId : purchaseOrderIdList) {
                    HandleDispatcher.route(BusinessTypeEnums.PURCHASE_ORDER_CHANGE.getCode(), String.valueOf(orderId), String.valueOf(recordId),
                            null, true, null, null);
                }
            }
        } catch (Exception e) {
            logger.error("采购订单变更出现异常", e);
            throw e;
        } finally {
            DistributedCASLock.unLock(lockName, currentDateStr);
        }

        return true;
    }

    /**
     * 保存历史版本
     *
     * @param purchaseOrderIdList
     * @param recordId
     * @param userBy
     */
    public void saveHistoryList(List<Long> purchaseOrderIdList, Long recordId, Long userBy) {
        //订单头
        PurchaseOrderExample example = new PurchaseOrderExample();
        example.createCriteria().andIdIn(purchaseOrderIdList);
        List<PurchaseOrderChangeHistory> historyList = BeanConverter.copy(purchaseOrderMapper.selectByExample(example),
                PurchaseOrderChangeHistory.class);
        for (PurchaseOrderChangeHistory orderChangeHistory : historyList) {
            orderChangeHistory.setOriginId(orderChangeHistory.getId());
            orderChangeHistory.setOriginCreateAt(orderChangeHistory.getCreateAt());
            orderChangeHistory.setId(null);
            orderChangeHistory.setRecordId(recordId);
            orderChangeHistory.setOrderStatus(PurchaseOrderStatus.APPROVED.code());
            orderChangeHistory.setSyncStatus(PurchaseOrderSyncStatus.SYNCED.code());
            orderChangeHistory.setHistoryType(HistoryType.HISTORY.getCode());
            orderChangeHistory.setCreateBy(userBy);
            orderChangeHistory.setUpdateBy(null);
            orderChangeHistory.setUpdateAt(null);
        }
        purchaseOrderChangeHistoryExtMapper.batchInsert(historyList);

        //订单行
        PurchaseOrderDetailExample detailExample = new PurchaseOrderDetailExample();
        detailExample.createCriteria().andPurchaseOrderIdIn(purchaseOrderIdList).andDeletedFlagEqualTo(DeletedFlag.VALID.code());
        List<PurchaseOrderDetailChangeHistory> detailHistoryList = BeanConverter.copy(purchaseOrderDetailMapper.selectByExample(detailExample),
                PurchaseOrderDetailChangeHistory.class);
        for (PurchaseOrderDetailChangeHistory orderDetailChangeHistory : detailHistoryList) {
            orderDetailChangeHistory.setOriginId(orderDetailChangeHistory.getId());
            orderDetailChangeHistory.setOriginCreateAt(orderDetailChangeHistory.getCreateAt());
            orderDetailChangeHistory.setOriginCreateByName(CacheDataUtils.findUserNameById(orderDetailChangeHistory.getCreateBy()));
            orderDetailChangeHistory.setId(null);
            orderDetailChangeHistory.setRecordId(recordId);
            orderDetailChangeHistory.setChangeStatus(PurchaseOrderDetailChangeHistoryChangeStatus.CHANGED.code());
            orderDetailChangeHistory.setHistoryType(HistoryType.HISTORY.getCode());
            orderDetailChangeHistory.setCreateBy(userBy);
            orderDetailChangeHistory.setUpdateBy(null);
            orderDetailChangeHistory.setUpdateAt(null);
        }
        purchaseOrderDetailChangeHistoryExtMapper.batchInsert(detailHistoryList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteBatchChangingOrder(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return false;
        }
        List<Long> requirementIdList = new ArrayList<>();
        List<Long> purchaseOrderIdList = new ArrayList<>();
        List<PurchaseOrderChangeHistory> purchaseOrderChangeHistoryList = new ArrayList<>();
        List<PurchaseOrderDetailChangeHistory> purchaseOrderDetailChangeHistoryList = new ArrayList<>();
        for (Long id : ids) {
            PurchaseOrderChangeHistory orderChangeHistory = purchaseOrderChangeHistoryMapper.selectByPrimaryKey(id);
            if (orderChangeHistory != null) {
                //已作删除的不处理
                if (Objects.equals(orderChangeHistory.getDeletedFlag(), DeletedFlag.INVALID.code())) {
                    continue;
                }
                //删除订单头
                orderChangeHistory.setDeletedFlag(DeletedFlag.INVALID.code());
                purchaseOrderChangeHistoryList.add(orderChangeHistory);
                purchaseOrderIdList.add(orderChangeHistory.getOriginId());
                //删除订单行
                PurchaseOrderDetailChangeHistoryExample detailExample = new PurchaseOrderDetailChangeHistoryExample();
                detailExample.createCriteria().andPurchaseOrderIdEqualTo(orderChangeHistory.getOriginId())
                        .andChangeStatusEqualTo(PurchaseOrderDetailChangeHistoryChangeStatus.CHANGING.code())
                        .andCreateByEqualTo(SystemContext.getUserId())
                        .andDeletedFlagEqualTo(DeletedFlag.VALID.code());
                List<PurchaseOrderDetailChangeHistory> orderDetailChangeHistoryList =
                        purchaseOrderDetailChangeHistoryMapper.selectByExample(detailExample);
                for (PurchaseOrderDetailChangeHistory orderDetailChangeHistory : orderDetailChangeHistoryList) {
                    orderDetailChangeHistory.setDeletedFlag(DeletedFlag.INVALID.code());
                    purchaseOrderDetailChangeHistoryList.add(orderDetailChangeHistory);
                    if (orderDetailChangeHistory.getOriginId() == null) {
                        requirementIdList.add(orderDetailChangeHistory.getMaterialPurchaseRequirementId());
                    }
                }
            }
        }
        if (!CollectionUtils.isEmpty(purchaseOrderChangeHistoryList)) {
            purchaseOrderChangeHistoryExtMapper.batchUpdate(purchaseOrderChangeHistoryList);
        }
        if (!CollectionUtils.isEmpty(purchaseOrderDetailChangeHistoryList)) {
            purchaseOrderDetailChangeHistoryExtMapper.batchUpdate(purchaseOrderDetailChangeHistoryList);
        }

        //更新订单状态
        if (!CollectionUtils.isEmpty(purchaseOrderIdList)) {
            List<PurchaseOrder> purchaseOrderList = new ArrayList<>();
            for (Long orderId : purchaseOrderIdList) {
                PurchaseOrder purchaseOrder = new PurchaseOrder();
                purchaseOrder.setId(orderId);
                purchaseOrder.setOrderStatus(PurchaseOrderStatus.APPROVED.code());
                purchaseOrderList.add(purchaseOrder);
            }
            purchaseOrderExtMapper.batchUpdate(purchaseOrderList);
        }

        //更新采购需求状态
        if (CollectionUtils.isNotEmpty(requirementIdList)) {
            purchaseMaterialRequirementService.batchUpdateStatus(requirementIdList);
        }
        return true;
    }

    @Override
    public PurchaseOrderChangeRecordDto getChangeRecordDetail(Long recordId, Long createBy) {
        //已提交变更的记录
        if (recordId != null) {
            PurchaseOrderChangeRecordDto record = BeanConverter.copy(purchaseOrderChangeRecordMapper.selectByPrimaryKey(recordId),
                    PurchaseOrderChangeRecordDto.class);
            Guard.notNull(record, "变更记录不存在");

            //订单行
            PurchaseOrderDetailChangeHistoryExample detailChangeHistoryExample = new PurchaseOrderDetailChangeHistoryExample();
            detailChangeHistoryExample.createCriteria().andRecordIdEqualTo(recordId).andHistoryTypeEqualTo(HistoryType.CHANGE.getCode()).andDeletedFlagEqualTo(DeletedFlag.VALID.code());
            List<PurchaseOrderDetailChangeHistoryDto> orderDetailChangeHistoryList =
                    BeanConverter.copy(purchaseOrderDetailChangeHistoryMapper.selectByExample(detailChangeHistoryExample),
                            PurchaseOrderDetailChangeHistoryDto.class);
            for (PurchaseOrderDetailChangeHistoryDto orderDetailChangeHistory : orderDetailChangeHistoryList) {
                if (orderDetailChangeHistory.getOriginId() != null) {
                    orderDetailChangeHistory.setCreateAt(orderDetailChangeHistory.getOriginCreateAt());          //原单据创建日期
                    orderDetailChangeHistory.setCreateByName(orderDetailChangeHistory.getOriginCreateByName());  //原单据创建人
                } else {
                    orderDetailChangeHistory.setCreateByName(CacheDataUtils.findUserNameById(orderDetailChangeHistory.getCreateBy()));
                }
            }
            Map<Long, List<PurchaseOrderDetailChangeHistoryDto>> orderDetailChangeHistoryMap =
                    orderDetailChangeHistoryList.stream().collect(Collectors.groupingBy(PurchaseOrderDetailChangeHistoryDto::getPurchaseOrderId));

            //订单头
            PurchaseOrderChangeHistoryExample example = new PurchaseOrderChangeHistoryExample();
            example.createCriteria().andRecordIdEqualTo(recordId).andHistoryTypeEqualTo(HistoryType.CHANGE.getCode()).andDeletedFlagEqualTo(DeletedFlag.VALID.code());
            List<PurchaseOrderChangeHistoryDto> orderChangeHistoryList =
                    BeanConverter.copy(purchaseOrderChangeHistoryMapper.selectByExample(example), PurchaseOrderChangeHistoryDto.class);
            record.setPurchaseOrderChangeHistoryDtos(orderChangeHistoryList);
            for (PurchaseOrderChangeHistoryDto orderChangeHistory : orderChangeHistoryList) {
                orderChangeHistory.setBuyerName(CacheDataUtils.findUserNameById(orderChangeHistory.getBuyerId()));  //采购员
                orderChangeHistory.setErpBuyerName(getErpBuyerName(orderChangeHistory.getErpBuyerId()));            //ERP采购员
                orderChangeHistory.setProjectOuName(CacheDataUtils.findOuNameById(orderChangeHistory.getOuId()));   //业务实体
                orderChangeHistory.setCreateAt(orderChangeHistory.getOriginCreateAt());                             //原单据创建日期
                orderChangeHistory.setPurchaseOrderDetailChangeHistoryDtos(orderDetailChangeHistoryMap.get(orderChangeHistory.getOriginId()));
            }
            return record;

            //待变更的记录
        } else {
            PurchaseOrderChangeRecordDto changingRecord = new PurchaseOrderChangeRecordDto();
            if (createBy != null) {
                List<PurchaseOrderChangeHistoryDto> changingOrderChangeHistoryList = getChangingOrder(createBy);
                changingRecord.setPurchaseOrderChangeHistoryDtos(changingOrderChangeHistoryList);
            }
            return changingRecord;
        }
    }

    @Override
    public Boolean batchDelete(List<PurchaseOrderDetailDto> detailDtos) {
        if (CollectionUtils.isEmpty(detailDtos)) return false;
        List<PurchaseOrderDetail> orderDetailList = new ArrayList<>();
        List<PurchaseOrderDetailChangeHistory> orderDetailChangeHistoryList = new ArrayList<>();
        Set<Long> changeOrderIdList = new HashSet<>();
        List<Long> requirementIdList = new ArrayList<>();

        // 删除待下达/待变更对应的订单行
        for (PurchaseOrderDetailDto detailDto : detailDtos) {
            if (Objects.equals(detailDto.getType(), 1)) { //待下达
                PurchaseOrderDetail item = new PurchaseOrderDetail();
                item.setId(detailDto.getId());
                item.setDeletedFlag(DeletedFlag.INVALID.code());
                orderDetailList.add(item);
            } else if (Objects.equals(detailDto.getType(), 2)) { //待变更
                PurchaseOrderDetailChangeHistory item = new PurchaseOrderDetailChangeHistory();
                item.setId(detailDto.getId());
                item.setDeletedFlag(DeletedFlag.INVALID.code());
                orderDetailChangeHistoryList.add(item);
                changeOrderIdList.add(detailDto.getPurchaseOrderId());
            }
            requirementIdList.add(detailDto.getMaterialPurchaseRequirementId());
        }
        if (CollectionUtils.isNotEmpty(orderDetailList)) {
            purchaseOrderDetailExtMapper.batchUpdate(orderDetailList);
        }
        if (CollectionUtils.isNotEmpty(orderDetailChangeHistoryList)) {
            purchaseOrderDetailChangeHistoryExtMapper.batchUpdate(orderDetailChangeHistoryList);
        }

        // 查询部分移除的订单
        List<Long> partialRemoveList = purchaseOrderDetailChangeHistoryExtMapper.selectPartialRemove(changeOrderIdList);

        // 全部移除的订单
        changeOrderIdList.removeAll(partialRemoveList);
        if (CollectionUtils.isNotEmpty(changeOrderIdList)) {
            //删除冗余的变更订单行
            purchaseOrderDetailChangeHistoryExtMapper.deleteByPurchaseOrderIds(changeOrderIdList);

            //删除冗余的变更订单头
            purchaseOrderChangeHistoryExtMapper.deleteByPurchaseOrderIds(changeOrderIdList);

            //更新订单头状态
            List<PurchaseOrder> orderList = new ArrayList<>();
            for (Long orderId : changeOrderIdList) {
                PurchaseOrder item = new PurchaseOrder();
                item.setId(orderId);
                item.setOrderStatus(PurchaseOrderStatus.APPROVED.code());
                orderList.add(item);
            }
            if (CollectionUtils.isNotEmpty(orderList)) {
                purchaseOrderExtMapper.batchUpdate(orderList);
            }
        }

        //更新采购需求状态
        if (CollectionUtils.isNotEmpty(requirementIdList)) {
            purchaseMaterialRequirementService.batchUpdateStatus(requirementIdList);
        }
        return true;
    }

    @Override
    public int updateChangeRecordStatus(Long recordId, Integer status, Integer syncStatus) {
        PurchaseOrderChangeRecord purchaseOrderChangeRecord = new PurchaseOrderChangeRecordDto();
        purchaseOrderChangeRecord.setId(recordId);
        purchaseOrderChangeRecord.setStatus(status);
        purchaseOrderChangeRecord.setSyncStatus(syncStatus);
        return purchaseOrderChangeRecordMapper.updateByPrimaryKeySelective(purchaseOrderChangeRecord);
    }

    private BigDecimal calculateDiscountedPrice(BigDecimal price, BigDecimal discount) {
        if (Objects.isNull(price)) {
            price = BigDecimal.ZERO;
        }
        if (Objects.isNull(discount)) {
            discount = BigDecimal.ZERO;
        }
        // 计算公式: 折后价: 单价*(1-折扣%)
        discount = new BigDecimal("1").subtract(discount.divide(new BigDecimal(100)));
        return price.multiply(discount);
    }

    private PurchaseBpaPriceQuery build(PurchaseOrderDto orderDto, PurchaseOrderDetailDto orderDetail) {
        PurchaseBpaPriceQuery priceQuery = new PurchaseBpaPriceQuery();
        priceQuery.setOuId(orderDto.getOuId());
        priceQuery.setCurrency(orderDto.getCurrency());
        priceQuery.setUnit(orderDetail.getUnit());
        priceQuery.setVendorSiteCode(orderDetail.getVendorSiteCode());
        priceQuery.setMaterialCode(orderDetail.getErpCode());
        return priceQuery;
    }

    /**
     * 是否满足更新一揽子的条件
     *
     * @param orderStatus 订单状态
     * @param pricingType 定价类型
     * @return
     */
    boolean isUpdatePrice(Integer orderStatus, Integer pricingType) {
        // 草稿、撤回、驳回状态 & 定价类型是一揽子,返回为true
        boolean res =
                (orderStatus == OrderStatusEnum.DRAFT.getCode() || orderStatus == OrderStatusEnum.REJECT.getCode() || orderStatus == OrderStatusEnum.CANCEL.getCode());
        return (res && pricingType == 2);
    }

    private List<UserInfoDto> getUserByUserNumber(Set<String> userNumbers) {
        String url = ModelsEnum.BASEDATA.getBaseUrl() + "employeeInfo/listUserByUserNumber";
        String res = restTemplate.postForObject(url, userNumbers, String.class);
        Asserts.notNull(res, Code.ERROR);
        return JSON.parseObject(res, new TypeReference<List<UserInfoDto>>() {
        });
    }

    private List<OrganizationRelDto> getOrganizationRelByOuName(List<String> ouNames) {
        String url = ModelsEnum.BASEDATA.getBaseUrl() + "organizationRel/listByOuNames";
        String res = restTemplate.postForObject(url, ouNames, String.class);
        Asserts.notNull(res, Code.ERROR);
        return JSON.parseObject(res, new TypeReference<List<OrganizationRelDto>>() {
        });
    }

    /**
     * 根据供应商编号查询供应商
     */
    private List<VendorSiteBankDto> getVendorSiteBankByCodes(List<String> codes) {
        String url = ModelsEnum.BASEDATA.getBaseUrl() + "vendor/getVendorSiteBankByCodes";
        String res = restTemplate.postForObject(url, codes, String.class);
        return JSON.parseObject(res, new TypeReference<List<VendorSiteBankDto>>() {
        });
    }

    private String getPamCodeMaterialKey(String pamCode, Long organizationId) {
        return String.format("getPamCodeMaterialKey_%s_%s", pamCode, organizationId);
    }

    private String buildProjectWbsReceiptsBudgetKey(String wbsSummaryCode, Long projectWbsReceiptsId) {
        return String.format("buildProjectWbsReceiptsBudgetKey_%s_%s", wbsSummaryCode, projectWbsReceiptsId);
    }

    @Override
    public Boolean isVendorMatchingConfigAndPushToFap(Long ouId, String vendorNum) {
        logger.info("检查采购订单启用关联交易供应商范围配置, ouId:{}, vendorNum:{}", ouId, vendorNum);
        Set<String> dictSet = organizationCustomDictService.queryByName("采购订单启用关联交易供应商范围", ouId, OrgCustomDictOrgFrom.OU);

        if (Objects.isNull(dictSet)) {
            logger.info("未找到检查采购订单启用关联交易供应商范围配置, ouId:{}", ouId);
            return false;
        }

        // 先尝试精确匹配
        boolean isMatched = dictSet.contains(vendorNum);

        // 如果精确匹配失败，尝试去除前后空格后匹配
        if (!isMatched && vendorNum != null) {
            String trimmedVendorNum = vendorNum.trim();
            isMatched = dictSet.stream()
                    .anyMatch(item -> item != null && item.trim().equals(trimmedVendorNum));
        }

        logger.info("检查采购订单启用关联交易供应商范围配置-供应商匹配结果: {}, ouId:{}, vendorNum:{}", isMatched, ouId, vendorNum);
        return isMatched;
    }

    /**
     * 更新采购订单同步到FAP系统状态
     *
     * @param orderId 采购订单ID
     * @return 更新结果
     */
    public Boolean updateSyncToFapStatus(Long orderId) {
        logger.info("采购订单同步FAP状态更新-开始处理, orderId:{}", orderId);

        try {
            // 参数校验
            Asserts.notEmpty(orderId, ErrorCode.ID_NOT_NULL);

            // 查询订单是否存在
            PurchaseOrder existingOrder = purchaseOrderMapper.selectByPrimaryKey(orderId);
            if (existingOrder == null) {
                logger.warn("采购订单同步FAP状态更新-订单不存在, orderId:{}", orderId);
                throw new BizException(Code.ERROR,"采购订单不存在");
            }

            // 构建更新对象
            PurchaseOrder updateOrder = new PurchaseOrder();
            updateOrder.setId(orderId);
            updateOrder.setSyncToFap(true);
            updateOrder.setUpdateBy(Optional.ofNullable(SystemContext.getUserId()).orElse(-1L));
            updateOrder.setUpdateAt(new Date());

            // 执行更新
            int updateCount = purchaseOrderMapper.updateByPrimaryKeySelective(updateOrder);

            if (updateCount > 0) {
                logger.info("采购订单同步FAP状态更新-更新成功, orderId:{}, syncToFap:true, updateBy:{}",
                    orderId, SystemContext.getUserId());
                return true;
            } else {
                logger.warn("采购订单同步FAP状态更新-更新失败, orderId:{}, updateCount:{}", orderId, updateCount);
                return false;
            }

        } catch (Exception e) {
            logger.error("采购订单同步FAP状态更新-处理异常, orderId:{}, error:{}", orderId, e.getMessage(), e);
            throw e;
        }
    }
}

