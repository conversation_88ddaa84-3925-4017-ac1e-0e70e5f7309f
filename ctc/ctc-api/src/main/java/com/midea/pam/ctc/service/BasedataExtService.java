package com.midea.pam.ctc.service;

import com.github.pagehelper.PageInfo;
import com.midea.pam.common.basedata.dto.*;
import com.midea.pam.common.basedata.entity.BrandMaintenance;
import com.midea.pam.common.basedata.entity.CoaSubject;
import com.midea.pam.common.basedata.entity.CustTrxType;
import com.midea.pam.common.basedata.entity.Dict;
import com.midea.pam.common.basedata.entity.FeeType;
import com.midea.pam.common.basedata.entity.LaborExternalCost;
import com.midea.pam.common.basedata.entity.MaterialPrice;
import com.midea.pam.common.basedata.entity.OperatingUnit;
import com.midea.pam.common.basedata.entity.OrgUnit;
import com.midea.pam.common.basedata.entity.OrganizationRel;
import com.midea.pam.common.basedata.entity.ProjectProductMaintenance;
import com.midea.pam.common.basedata.entity.Storage;
import com.midea.pam.common.basedata.entity.Unit;
import com.midea.pam.common.basedata.entity.UserInfo;
import com.midea.pam.common.basedata.entity.UserMesDto;
import com.midea.pam.common.basedata.entity.VendorMip;
import com.midea.pam.common.basedata.entity.VendorSiteBank;
import com.midea.pam.common.basedata.entity.VendorSiteBankForDisplay;
import com.midea.pam.common.basedata.query.BankAccountQuery;
import com.midea.pam.common.basedata.query.GlDailyRateQuery;
import com.midea.pam.common.basedata.query.InventoryQuery;
import com.midea.pam.common.basedata.query.OrganizationRelQuery;
import com.midea.pam.common.ctc.dto.BudgetHumanDto;
import com.midea.pam.common.ctc.dto.WorkingHourQueryDto;
import com.midea.pam.common.ctc.dto.WorkingHourResultDto;
import com.midea.pam.common.ctc.entity.OrganizationCustomDict;
import com.midea.pam.common.ctc.entity.VendorAslCtc;
import com.midea.pam.common.ctc.vo.EsbUnitVo;
import org.apache.commons.lang3.tuple.Pair;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

public interface BasedataExtService {
    List<OrgUnit> getOrgUnit();

    /**
     * 根据虚拟单位ID获取单据前缀(美云:M，机器人:R)
     *
     * @param id
     * @return String
     */
    String getUnitSeqPerfix(Long id);

    /**
     * 查询单位
     *
     * @param updateAt
     * @return List<EsbUnitVo>
     */
    List<EsbUnitVo> queryEsbUnit(String updateAt);

    CoaSubject getCoaSubject(String flexValue);


    /**
     * 根据vendorId获取供应商对象
     *
     * @param id
     * @return VendorSiteBankDto
     */
    VendorSiteBankDto getVendorSiteBankDto(Long id);

    /**
     * 根据三个参数获取供应商对象
     *
     * @param ouId
     * @param vendorSiteCode
     * @param vendorName
     */
    VendorSiteBankDto getCurrencyCode(Long ouId, String vendorSiteCode, String vendorName);

    /**
     * 查询应收事务处理类型
     *
     * @param custTrxTypeIdName
     * @param trxType
     * @return
     */
    List<CustTrxType> getCustTrxType(String custTrxTypeIdName, String trxType, Long ouId);


    /**
     * 根据业务实体ID获取供应商对象
     *
     * @param id 业务实体ID
     * @return OperatingUnit
     */
    OperatingUnit findOperatingUnitById(Long id);

    /**
     * 查询当前使用单位下有效的供应商对象
     *
     * @return OperatingUnit
     */
    List<OperatingUnitDto> queryCurrentUnitOu();

    List<OperatingUnitDto> queryCurrentUnitOu(Long unitId);

    /**
     * 查询当前使用单位下有效的供应商对象
     *
     * @return String
     */
    String queryCurrentStrUnitOu();


    /**
     * 根据字典ID获取对象
     *
     * @param id 字典ID
     * @return Dict
     */
    Dict findDictById(Long id);

    List<DictDto> findDictByType();


    /**
     * 查询工时填报预警
     *
     * @param query
     * @return
     */
    List<WorkingHourResultDto> selectSubmitRemind(WorkingHourQueryDto query);

    /**
     * 根据条件查询银行账号信息
     *
     * @param query
     * @return
     */
    List<BankAccountDto> findBankAccount(BankAccountQuery query);

    /**
     * 根据字典类型以及编码获取字典对象
     *
     * @param type 字典类型
     * @param code 字典编码
     * @return Dict
     */
    Dict findDictByTypeAndCode(String type, String code);

    /**
     * 根据配置对象id以及对象来源查询参数列表
     *
     * @param orgId   配置对象id
     * @param orgFrom 参数列表
     * @return List<OrganizationCustomDict>
     */
    List<OrganizationCustomDict> getOrganizationCustomDictList(Long orgId, String orgFrom);

    /**
     * 根据配置对象id以及对象来源查询参数列表以及参数CODE
     *
     * @param orgId   配置对象id
     * @param orgFrom 参数对象
     * @return OrganizationCustomDict
     */
    OrganizationCustomDict getOrganizationCustomDictByCode(Long orgId, String orgFrom, String code);

    /**
     * 根据配置对象id以及对象来源查询参数列表以及参数名称
     *
     * @param orgId   配置对象id
     * @param orgFrom 参数对象
     * @return OrganizationCustomDict
     */
    OrganizationCustomDict getOrganizationCustomDictByName(Long orgId, String orgFrom, String name);

    /**
     * 获取ERP组织参数配置
     *
     * @param query
     * @return
     */
    List<OrganizationRelDto> getOrganizationRel(OrganizationRelQuery query);

    /**
     * 根据ouID与收款方法ID来获取收款方法名称
     *
     * @param operatingUnitId ouID
     * @param receiptMethodId 收款方法ID
     * @return
     */
    public List<ReceiptMethodDto> getReceiptMethod(Long operatingUnitId, Long receiptMethodId, String receiptMethodName, String currencyCode);

    /**
     * 获取虚拟单位信息
     *
     * @param id       单位ID
     * @param parentId 父级单位ID
     * @return 虚拟单位信息
     */
    List<UnitDto> findUnitDtos(String id, String parentId);

    /**
     * 获取人力费用
     *
     * @return 虚拟单位信息
     */
    LaborCostDto getLaborCost();

    List<LaborCostDto> getLaborCostList(Long unitId);

    /**
     * 获取外部人力费用
     *
     * @return 虚拟单位信息
     */
    LaborExternalCost getLaborExternalCost(Long costTypeId, String vendorName);

    /**
     * 根据虚拟单位ID获取所有二级部门ID
     *
     * @param parentId 父级单位ID
     * @return 虚拟单位信息
     */
    List<Long> findUnitIds(Long parentId);

    /**
     * 根据虚拟单位ID获取所有二级部门ID
     *
     * @param parentId 父级单位ID
     * @return 虚拟单位信息
     */
    String findStrUnitIds(Long parentId);

    /**
     * 获取授权的二级部门ID
     *
     * @return 虚拟单位信息
     */
    String findAuthorStrUnitIds();

    /**
     * 同步pam_basedata的vendorasl表
     *
     * @param vendorAsl
     */
    void syncVendorAsl(VendorAslCtc vendorAsl);

    /**
     * 通过用户ID查询用户信息
     *
     * @param userId
     * @return UserDetailsDto
     */
    UserDetailsDto findUserInfo(Long userId);

    /**
     * 获取数据字典信息
     *
     * @param type 类型
     * @param name 名称，非必填
     * @param code 代码，非必填
     * @return 数据字典信息
     */
    List<DictDto> getLtcDict(String type, String name, String code);

    /**
     * 根据项目编号查询子库库存量不为0
     *
     * @param projectCode
     * @param ouId
     * @return
     */
    List<Storage> getNotNullByProCode(String projectCode, Long ouId);

    /**
     * 查询会计期间
     *
     * @param ledgerId   ERP分类账ID
     * @param periodType 期间类型
     * @param periodName 期间名称
     * @return
     */
    List<GlPeriodDto> getGlPeriod(Long ledgerId, String periodType, String periodName);

    List<GlPeriodDto> getGlPeriod2(Long ledgerId, String periodType, String closingStatus);

    List<GlPeriodDto> getGlPeriodByOrganizationId(Long organizationId, String periodType, String periodName);

    List<VendorSiteBank> getByErpVendorSiteIds(String erpVendorSiteIds);

    List<BudgetHumanDto> queryLabCostByUserId(String priceType, String userIds, String unitId);

    List<UserInfo> getAvailableUser();

    PageInfo<GlDailyRateDto> queryGlDailyRate(String fromCurrency, String toCurrency, Date exchangeDate);

    List<GlPeriodDto> getGlPeriod1(Long ledgerId, String periodType, String periodName, Long periodYear, String glPeriodDate);

    /**
     * 查找用户对应的hr部门以及费率类型
     *
     * @param orgId
     * @param orgName
     * @param name
     * @param username
     * @param userId
     * @return
     */
    List<UserInfoDto> listInfo1(String orgId, String orgName, String name, String username, Long userId);

    /**
     * 查找用户对应的pam业务分类
     *
     * @param companyId
     * @param unitId
     * @param hrOrgName
     * @param laborCostTypeCode
     * @param laborCostTypeCodes
     * @param unitIds
     * @return
     */
    List<OrgLaborCostTypeSetDTO> findOrgLaborCostTypes(Long companyId,
                                                       Long unitId,
                                                       String hrOrgName,
                                                       String laborCostTypeCode,
                                                       String laborCostTypeCodes,
                                                       String unitIds);

    /**
     * 查询有效的人力费率明细
     *
     * @param bizUnitId
     * @param month
     * @param levelName
     * @return
     */
    List<LaborCostRankRealMonthDetailDto> getValidDetails(Long bizUnitId, String month, String levelName);

    List<VendorSiteBankDto> getVendorSiteBankDtoList(Long id, String vendorCode, Long operatingUnitId);

    /**
     * 查询用户的级别及费率类型
     *
     * @param userIds
     * @return
     */
    Map<Long, UserMesDto> getUserLaborCostType(String userIds);

    /**
     * 查询当前使用单位用户有权限的ou对应的库存组织.
     *
     * @return 库存组织
     */
    List<OrganizationRelDto> selectCurrentOrganization();

    OrgLaborCostTypeSetDTO findOrgLaborCostTypeSetById(Long id, Long ouId);

    VendorSiteBankDto getVendorSiteBankInfo(Long ouId, String vendorBankNum, String vendorCode, String accountName);

    VendorSiteBankForDisplay getVendorSiteBankStatus(Long ouId, String vendorCode, String erpVendorSiteId);

    List<VendorSiteBankDto> getVendorSiteBankByCodes(List<String> vendorCodes);

    public List<VendorSiteBankDto> getVendorSiteBankByCode(List<String> vendorCodes);

    Unit selectByPrimaryKey(Long id);

    List<StorageInventoryDto> selectStorageInventoryList(String inventoryType);

    /**
     * 根据子库类型和子库资产查询可用子库列表
     *
     * @param inventoryType
     * @param assetInventory
     * @return
     */
    List<StorageInventoryDto> selectStorageInventoryListByTypeAndAssetInventory(String inventoryType, String assetInventory);

    LaborCostDto getLaborCostYL(Long bizUnitId, Integer type, Long userId);

    /**
     * 查询配置了组织参数[物料编码规则]的使用单位所属的生效ouid
     *
     * @return
     */
    List<Long> getMaterialAvailableOuIds();

    /**
     * 查询配置了组织参数[物料编码规则]的使用单位所属的生效业务实体
     *
     * @return
     */
    List<OperatingUnitDto> getMaterialAvailableOperatingUnitList();

    ProjectProductMaintenance getProjectProductMaintenance(Long id);

    List<BrandMaintenance> getBrandMaintenance();

    void updateMilepostDesignPlanMiddle(Long markId);


    List<CustTrxType> getByOuId(Long ouId);

    List<OrganizationRel> queryByOuId(Long ouId);

    String queryLocalCurrencyByOuId(Long ouId);

    Long getIdByOrganizationIdAndItemCode(Long orgId, String materialCode);

    List<MaterialDto> listByItemCodesAndOrganizationIds(List<MaterialDto> materialDtos);

    Map<String, OrganizationRel> queryCurrentUnitOrganization();

    List<OrganizationRel> getValidOrganization();

    List<OperatingUnitDto> getOuInfoByOuIds(List<Long> ouIds);

    /**
     * 根据单位id查询ou
     *
     * @param unitId 单位id
     * @return ou
     */
    List<OperatingUnitDto> getOperatingUnitByUnitId(Long unitId);

    /**
     * 获取所有有效的ou
     *
     * @return ou
     */
    List<OperatingUnitDto> getAllOperatingUnit();

    /**
     * 根据ouId查询使用单位
     *
     * @param ouId 业务实体id
     * @return ou单位
     */
    OperatingUnitDto queryUnitByOuId(Long ouId);

    /**
     * 查询汇率
     *
     * @param query 查询条件
     * @return 汇率信息
     */
    GlDailyRateDto queryGlDailyRate(GlDailyRateQuery query);

    DiffCompanyLaborCostSetDTO getDiffCompanyLaborCostSet(Long bizUnitId, Long laborCostSourceUnitId, Long ouId);

    BankAccountDto getBankAccountByBankAccountId(Long bankAccountId);

    List<VendorMip> getVendorMipList(String vendorCodeStr);

    Map<String, BigDecimal> getMaterialPriceByConfigHeader(MaterialPriceDto dto);

    Map<String, MaterialPrice> getMaterialPriceEntityByConfigHeader(MaterialPriceDto dto);

    Map<Long, Long> getMaterialByIds(List<Long> idList);

    List<FeeItemDto> queryFeeItemList(Long unitId, String name);

    List<CoaSubjectDto> selectAllUnitCoaSubject();

    Pair<String, String> getBudgetProjectNumberByVendorId(Long purchaseContractId, Long vendorId, Long ouId);

    /**
     * 根据单位id和物料编码查询物料的货架
     *
     * @param organizationId
     * @param itemCodes
     * @return
     */
    Map<String, String> getMaterialShelvesByOrgIdAndItemCode(Long organizationId, List<String> itemCodes);

    /**
     * 根据物料id查询物料信息
     *
     * @param materialIds
     * @return
     */
    List<MaterialDto> getMaterialListByMaterialIds(List<Long> materialIds);

    List<FeeType> getFeeTypeByIds(List<Long> feeTypeIds);

    /**
     * 根据公司编码查询组织关系
     * @param companyCodeList
     * @return
     */
    public List<OrganizationRelDto> getOrganizationRelByCompanyCode(List<String> companyCodeList);

    /**
     * 查询子库信息
     * @param query
     * @return
     */
    List<InventoryDto> selectInventoryList(InventoryQuery query);
}
