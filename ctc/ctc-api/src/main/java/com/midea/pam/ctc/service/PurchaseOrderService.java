package com.midea.pam.ctc.service;

import com.midea.pam.common.basedata.entity.MaterialPrice;
import com.midea.pam.common.constants.ResponseMap;
import com.midea.pam.common.ctc.dto.PurchaseDemandOrderIssudeDto;
import com.midea.pam.common.ctc.dto.PurchaseOrderChangeHistoryDto;
import com.midea.pam.common.ctc.dto.PurchaseOrderChangeRecordDto;
import com.midea.pam.common.ctc.dto.PurchaseOrderDetailDto;
import com.midea.pam.common.ctc.dto.PurchaseOrderDto;
import com.midea.pam.common.ctc.dto.PurchaseOrderReceiptsDto;
import com.midea.pam.common.ctc.dto.SaveBatchPurchaseOrderRecordDto;
import com.midea.pam.common.ctc.dto.SdpCheckDto;
import com.midea.pam.common.ctc.excelVo.PurchaseOrderDetailImportVO;
import com.midea.pam.common.ctc.excelVo.PurchaseOrderImportVO;
import com.midea.pam.common.ctc.vo.AssociateOccupiedBudgetDetailsVo;
import com.midea.pam.common.ctc.vo.PurchaseDemandOrderIssudeVo;
import com.midea.pam.common.ctc.vo.PurchaseOrderPdfVO;
import com.midea.pam.common.ctc.vo.SdpCheckVo;
import com.sun.org.apache.xpath.internal.operations.Bool;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

public interface PurchaseOrderService extends BaseService<PurchaseOrderDto> {

    /**
     * 获取采购员的待下达订单
     *
     * @param buyerBy
     * @return
     */
    List<PurchaseOrderDto> getPendingOrder(Long buyerBy) throws Exception;

    /**
     * 保存订单/更新订单明细
     *
     * @param dto
     * @param userBy
     * @return
     */
    PurchaseOrderDto saveWithDetail(PurchaseOrderDto dto, Long userBy);

    /**
     * 批量保存订单/更新订单明细
     *
     * @param dtos
     * @param userBy
     * @return
     */
    List<PurchaseOrderDto> saveWithDetailBatch(List<PurchaseOrderDto> dtos, Long userBy);

    PurchaseOrderDto getDetailById(Long id);

    String getErpBuyerName(Long erpBuyerId);

    PurchaseOrderDto buildPushOrderDto(Long purchaseOrderId, Long changeRecordId);

    PurchaseOrderDto getDetailWbsById(Long id);

    Long getRequirementIdByFilter(String itemCode, Long projectId, Date deliveryTime);

    /**
     * 获取物料的(已下单)下达量
     *
     * @param pamCode      物料pam编码
     * @param projectId    项目id
     * @param deliveryTime 交付时间
     * @return
     */
    BigDecimal getOrderNumSum(String pamCode, Long projectId, Date deliveryTime);

    /**
     * 定时查询采购订单状态
     *
     * @param date
     */
    void asynStatusFromErp(final String date);

    /**
     * 定时拉取erp采购订单数据
     *
     * @param date
     */
    void asynDataFromErp(final String date);

    /**
     * 手工同步采购订单
     *
     * @param id
     * @param userBy
     * @return
     */
    Boolean pushToErp(Long id, Long userBy);

    Boolean pushToErpSync(Long id, Long userBy);

    Boolean pushToErpChange(Long id, Long userBy);

    /**
     * 手工重推采购订单
     *
     * @param id
     * @param userBy
     * @return
     */
    Boolean resendToErp(Long id, Long userBy);

    /**
     * 手工取消采购订单
     *
     * @param id
     * @param userBy
     * @return
     */
    Boolean cancelOrder(Long id, Long userBy);

    /**
     * 批量保存采购订单状态记录
     *
     * @param purchaseOrderRecordDto
     * @return
     */
    Long saveBatchPurchaseOrderRecord(SaveBatchPurchaseOrderRecordDto purchaseOrderRecordDto, Integer status);

    /**
     * 非接口调用,根据实例id修改
     *
     * @param saveBatchPurchaseOrderRecordDto
     * @param status
     * @return
     */
    Long newReceiptsIdUpdate(SaveBatchPurchaseOrderRecordDto saveBatchPurchaseOrderRecordDto, Integer status);

    /**
     * 非接口调用,新建保存
     *
     * @param purchaseOrderRecordDto
     * @param id
     * @param status
     */
    void newSave(List<PurchaseOrderReceiptsDto> purchaseOrderRecordDto, Long id, Integer status);

    /**
     * 采购订单详情(重新编辑)
     *
     * @param receiptsId
     * @return
     */
    SaveBatchPurchaseOrderRecordDto getDetails(Long receiptsId, Integer editFlag);

    /**
     * @param projectWbsReceiptsId
     * @param wbsSummaryCode
     * @param num
     * @return
     */
    List<AssociateOccupiedBudgetDetailsVo> associateOccupiedBudgetDetails(Long projectWbsReceiptsId, String wbsSummaryCode, String num);

    List<AssociateOccupiedBudgetDetailsVo> associateOccupiedBudgetContractDetails(Long projectWbsReceiptsId, String wbsSummaryCode);

    List<PurchaseDemandOrderIssudeVo> getMergeOrder(String id);

    Long setMergeOrder(List<PurchaseDemandOrderIssudeDto> list);

    PurchaseOrderDetailDto setGetMergeOrderDetail(List<PurchaseOrderDetailDto> list);

    SdpCheckVo sdpCheck(List<SdpCheckDto> sdpCheckDto);

    List<Long> updateByReceiptsIdStatus(Long receiptsId);

    void setPurchaseOrderDetailRemainMoney(List<PurchaseOrderDetailDto> purchaseOrderDetailList);

    void calculateRemainMoney(PurchaseOrderDetailDto orderDetail, Map<String, List<PurchaseOrderDetailDto>> progressNumMap);

    /**
     * 对比物料下达数量与采购需求未下达量
     *
     * @param erpCode
     * @param purchaseRequirementId
     * @param orderNum
     */
    void checkRequirementNum(String erpCode, Long purchaseRequirementId, BigDecimal orderNum);

    List<PurchaseOrderChangeRecordDto> getVersionHistoryList(Long purchaseOrderId);

    /**
     * @param id:          采购订单ID或purchase_order_change_history.record_id
     * @param tag:         版本标识
     * @param approveInfo: 核准人
     * @description 采购订单打印-获取PDF所需数据
     */
    PurchaseOrderPdfVO getVersionHistoryDetail(Long id, Integer tag, String approveInfo);

    /**
     * @param id:  采购订单ID或purchase_order_change_history.record_id
     * @param tag: 版本标识
     * @description 查找采购订单下达或变更的流程ID
     */
    String getDdInstanceId(Long id, Integer tag);

    List<PurchaseOrderImportVO> importPurchaseOrder(List<PurchaseOrderImportVO> excelVoList);

    List<PurchaseOrderDetailImportVO> importPurchaseOrderDetail(List<PurchaseOrderDetailImportVO> excelVoList, Long unitId);

    Boolean updateApproveInfoByPurchaseOrderReceiptsId(Long receiptsId, String approveInfo);

    /**
     * 根据项目id查询所有物料需求的已下达量(erpcode+DeliveryTime --> 已下达量)
     *
     * @param projectId
     * @return
     */
    Map<String, BigDecimal> queryOrderNumMapByProjectId(Long projectId);

    /**
     * 移动审批采购下单详情
     *
     * @param receiptsId
     * @return
     */
    ResponseMap getMobileApprovalDetail(Long receiptsId);

    /**
     * 根据单据Id查询订单
     *
     * @param receiptsId
     * @return
     */
    List<PurchaseOrderDto> getOrderByReceiptsId(Long receiptsId);

    /**
     * 根据需求变更单据回写采购订单行预算占用金额
     *
     * @param publishReceiptIdList 需求发布预算信息
     */
    void updateBudgetOccupiedAmount(List<Long> publishReceiptIdList);

    List<PurchaseOrderChangeHistoryDto> getChangingOrder(Long buyerBy);

    Boolean deleteBatchChangingOrder(List<Long> ids);

    Boolean changeWithDetailBatch(PurchaseOrderChangeRecordDto dto, Long userBy);

    PurchaseOrderChangeRecordDto getChangeRecordDetail(Long recordId, Long createBy);

    Boolean batchDelete(List<PurchaseOrderDetailDto> purchaseOrderDetailDtos);

    int updateChangeRecordStatus(Long record, Integer status, Integer SyncStatus);

    List<PurchaseOrderDetailDto> queryHistoryPrice(List<PurchaseOrderDetailDto> list);

    Map<String, BigDecimal> getMaterialPriceMap(List<PurchaseOrderDetailDto> detailList, List<Long> orgErrorList);

    Map<String, MaterialPrice> getMaterialPriceEntityMap(List<PurchaseOrderDetailDto> detailList,List<Long> orgErrorList);

    /**
     * 判断采购订单供应商是否符合推送至FAP条件
     *
     * @param ouId
     * @param vendorNum
     * @return
     */
    Boolean isVendorMatchingConfigAndPushToFap(Long ouId,String vendorNum);

    /**
     * 更新采购订单同步到FAP系统状态
     *
     * @param orderId 采购订单ID
     * @return 更新结果
     */
    Boolean updateSyncToFapStatus(Long orderId);
}
